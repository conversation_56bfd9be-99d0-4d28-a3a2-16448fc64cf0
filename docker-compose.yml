services:
  neo4j:
    image: neo4j:latest
    container_name: neo4j_container
    ports:
      - ${NEO4J_HTTP_PORT}:7474
      - ${NEO4J_BOLT_PORT}:7687
    environment:
      NEO4J_AUTH: ${NEO4J_USERNAME}/${NEO4J_PASSWORD}
      NEO4J_PLUGINS: '["apoc"]'
    volumes:
      - ${NEO4J_DATA_PATH}:/data
      - ${NEO4J_IMPORT_PATH}:/var/lib/neo4j/import
    networks:
      - app_network

  # redis:
  #   image: redis:latest
  #   container_name: redis_container
  #   ports:
  #     - 6379:6379
  #   networks:
  #     - app_network

  # node_app:
  #   image: node:latest
  #   container_name: node_app_container
  #   working_dir: /usr/src/app
  #   volumes:
  #     - ${NODE_APP_PATH}:/usr/src/app
  #   # command: bash -c "curl -fsSL https://bun.sh/install | bash && export PATH=\"$HOME/.bun/bin:$PATH\" && bun install && bun run dev"
  #   command: bash -c "if ! command -v bun &> /dev/null; then curl -fsSL https://bun.sh/install | bash; fi && export PATH=\"$HOME/.bun/bin:$PATH\" && bun install && bun run start"
  #   ports:
  #     - ${NODE_APP_PORT}:3000
  #   networks:
  #     - app_network

networks:
  app_network:
    driver: bridge
