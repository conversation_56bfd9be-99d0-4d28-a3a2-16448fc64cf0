# Kanban

## Running

To run locally use

```sh
npm run dev
```

then navigate to http://localhost:3000 in the browser.
NOTE: If the test-oidc-web app is running it will grab port 3000, so the port might be 3001

Right now the oidc app does NOT really authenticate, it only checks that the UN and PWD are the same

## History

We looked at the various monorepos listed here:
https://graphite.dev/guides/monorepo-tools-a-comprehensive-comparison

We tried them all but turbo seemed to be the simplest while still offering all features

This application was bootstrapped using

```sh
npx create-turbo@latest
```

Then we deleted the docs app, and created api and oidc apps using commands like:

```sh
npx turbo gen workspace --name api --type app
npx turbo gen workspace --name oidc --type app
npx turbo gen workspace --name shared-types --type package
```

BUG: when creating a package using the above command it does not prefix that package name with @repo (like it should)

#### Managing Dependencies

https://turbo.build/repo/docs/crafting-your-repository/managing-dependencies

Project dependencies should be installed at the project level

Installing same dependendcy across multiple project

```sh
bun install jest --workspace=web --workspace=@repo/ui --save-dev
```

Installing actoss all workspaces

```sh
bun install typescript@latest --workspaces
```

## What's inside?

This Turborepo includes the following packages/apps:

### Apps and Packages

- `web`: another [Next.js](https://nextjs.org/) app
- `@repo/ui`: a stub React component library shared by both `web` and `docs` applications
- `@repo/eslint-config`: `eslint` configurations (includes `eslint-config-next` and `eslint-config-prettier`)
- `@repo/typescript-config`: `tsconfig.json`s used throughout the monorepo

Each package/app is 100% [TypeScript](https://www.typescriptlang.org/).

### Utilities

This Turborepo has some additional tools already setup for you:

- [TypeScript](https://www.typescriptlang.org/) for static type checking
- [ESLint](https://eslint.org/) for code linting
- [Prettier](https://prettier.io) for code formatting

### Build

To build all apps and packages, run the following command:

```
pnpm build
```

### Develop

To develop all apps and packages, run the following command:

```
pnpm dev
```

### Remote Caching

Turborepo can use a technique known as [Remote Caching](https://turbo.build/repo/docs/core-concepts/remote-caching) to share cache artifacts across machines, enabling you to share build caches with your team and CI/CD pipelines.

By default, Turborepo will cache locally. To enable Remote Caching you will need an account with Vercel. If you don't have an account you can [create one](https://vercel.com/signup), then enter the following commands:

```
npx turbo login
```

This will authenticate the Turborepo CLI with your [Vercel account](https://vercel.com/docs/concepts/personal-accounts/overview).

Next, you can link your Turborepo to your Remote Cache by running the following command from the root of your Turborepo:

```
npx turbo link
```

## Useful Links

Learn more about the power of Turborepo:

- [Tasks](https://turbo.build/repo/docs/core-concepts/monorepos/running-tasks)
- [Caching](https://turbo.build/repo/docs/core-concepts/caching)
- [Remote Caching](https://turbo.build/repo/docs/core-concepts/remote-caching)
- [Filtering](https://turbo.build/repo/docs/core-concepts/monorepos/filtering)
- [Configuration Options](https://turbo.build/repo/docs/reference/configuration)
- [CLI Usage](https://turbo.build/repo/docs/reference/command-line-reference)


## Bun fixes
Occasionally the bun command will not work, and you will get an error like this:

```sh
error: No version matching "^6.2.0" found for specifier "@mui/core-downloads-tracker" (but package exists)
error: @mui/core-downloads-tracker@^6.2.0 failed to resolve
```
Run this to clear you cache and try again
```sh
bun pm cache rm
```

## docker-compose

Create a .env files and add the following variables with appropriate values 
```
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=
NEO4J_HTTP_PORT=7475
NEO4J_BOLT_PORT=7688
NEO4J_DATA_PATH=./data
NEO4J_IMPORT_PATH=./import

# Node Application Configuration
NODE_APP_PORT=3000
NODE_APP_PATH=./apps/
```

```sh
docker compose up -d
```