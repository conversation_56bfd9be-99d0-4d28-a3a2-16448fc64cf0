import Image from "next/image";
import styles from "./page.module.css";
import SignIn from "./components/sign-in";
import UserAvatar from "./components/user-avatar";
import { auth } from "../auth";
import { SignOut } from "./components/sign-out";

export default async function Home() {
  const session = await auth();

  return (
    <main className={styles.main}>
      {!session?.user && <SignIn />}
      {session?.user && (
        <>
          <UserAvatar />
          <SignOut />
        </>
      )}
    </main>
  );
}
