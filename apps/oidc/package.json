{"name": "oidc", "version": "0.0.0", "private": true, "main": "dist/index.js", "type": "module", "scripts": {"build": "rimraf dist && tsc", "start": "node dist/index.js", "dev": "tsx src/index.ts"}, "dependencies": {"@repo/eslint-config": "*", "@repo/typescript-config": "*", "@repo/ui": "*", "dotenv": "^16.4.5", "ejs": "^3.1.10", "express": "^4.19.2", "oidc-provider": "^8.5.1"}, "devDependencies": {"@types/ejs": "^3.1.5", "@types/express": "^4.17.21", "@types/node": "^22.1.0", "@types/oidc-provider": "^8.5.1", "rimraf": "^6.0.1", "tsx": "^4.17.0", "typescript": "^5"}}