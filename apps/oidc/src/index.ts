import express from "express";
import { InteractionResults, Provider } from "oidc-provider";
import path from "path";
import dotenv from "dotenv";
import url from "url";

dotenv.config();
dotenv.config({ path: `.env.local`, override: true });

const app = express();
const port = process.env.PORT || 3000;
//Middlewares
const __filename = url.fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

app.use(express.static(__dirname + "/public"));
app.set("views", path.join(__dirname, "views"));
app.set("view engine", "ejs");

app.use(express.urlencoded({ extended: true }));
app.use(express.json());
import { configuration } from "./auth/oidc-configuration.js";

const issuer = `http://localhost:${port}/oidc`;

const oidc = new Provider(issuer, configuration);

app.use("/oidc", oidc.callback());

app.get("/interaction/:uid", async (req, res, next) => {
  res.render("login", { uid: req.params.uid });
});

app.post("/interaction/:uid", async (req, res, next) => {
  // TODO: Need to make sure UN/PWD are valid
  const email = req.body.email;
  const results: InteractionResults = {
    login: {
      accountId: email,
      token: "token",
      acr: "acr",
      amr: ["amr"],
      remember: !!req.body.remember,
      ts: Math.floor(Date.now() / 1000),
    },
  };
  if (req.body) {
    await oidc.interactionFinished(req, res, results);
  }
});

app.listen(port, function () {
  console.log(
    `[server]: OIDC Server is running at http://localhost:${port}/oidc/.well-known/openid-configuration`
  );
});
