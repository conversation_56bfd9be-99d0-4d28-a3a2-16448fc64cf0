//TODO - This is a mock jwks file. It should be replaced with a certificate generated as part of one time booststrap process.
export const jwks = {
  "keys": [
    {
      "p": "y7Z1N0-RfcWVL_aPpFyVEIpQ-5mYrYSExxdUKDWy6rqwotUaNYXDaxc-90cGLgxJqDa-352vhhlvu6792w1IKR4j0CnDNSplxJmIjSbBZ8-23GCohSDLvrbEsZ4r19HytwtV2zfo0crvCRammCeSuZyxiX3P4DnUdd3M7jpsxwM",
      "kty": "RSA",
      "q": "vhp-htd9j5NRel_hFNocqRo_sya2Fx2PZAp5g44ZoYV3WYVmHLZ5UWveXOxphWM2pCVMAP68vqRBmqkx6vuROgBisdh6D41FZRVqBZi4ySiAQqHuTITDq7meNYVXEBCwONeO4Dq4d5FQsD-fBnvyhyXtAsjJAK93PqfeJFCIvUc",
      "d": "dTG1GiyBiadXS04mX93Bh7hc1JqjK8hlVEX8KmpBH78Btam09kFUkNAbMJrxyy0wH1Xo6YnOegNQ8nVOtbb99TbCn1cQdidOtBfMtMhyq3gwDXqlyqFl9i8Zqna8h1_ZYGtp9PFLZ_Ny7ohW8kfgqC7cUAl9MhDuNWr9_TGjFlkXmXcJ_CqsAB3hervaUezaQ-0aPUPSbjJEhZ4LL3WInDq6YLZYH5ESzNAZc_i_I3vQe1qRlQNRQSzNC3sfNwpUQggXK2WmPtN-1EZPnzzoddcIojgD1XK83WS35EcE9oAkKeUV4S7ZsbT-iogMSbjL1uM5RIV3SlBNGEnFWCZF8Q",
      "e": "AQAB",
      "use": "sig",
      "kid": "FROiU4J34VqTTAihJHVlNvvBTpNDJBqwBkdvmwvSG9k",
      "qi": "l9TZ7FV2a7C9BNz9SGNyKoUZ4xNdk8dApvwb_BL5XIXQLYODSI4fLaWq5SvTMa-lxil4zrs0qNEHbFoM9XtpoUJUCN0-QEWVFJ24fmTs59JlTe4I9zGJvr0-rTdjVxFnVpg3SHhsIVEhBQYO_u9Sw46QjV2l1omhwlnWjP80wM0",
      "dp": "j3tj9CFHCJ8yBSZU5h-PnqbNg2kb8Jt2KgR0ch3gdxQndYlDfI9KpRMqdmDAKY7eEmz84-PczUb9O-R1SOSB_vQLpYLEy52hMGgzlbfGYagb0_0AtUP0Pc_dmn0dxFVL70E8p7spcRrOYkhh7qlw3utO4NiMohIZYytmPCVKmKE",
      "alg": "RS256",
      "dq": "BBlEIxogTekqN447pxxww8CaIP2zrVnozopfRlPY_5-GDDsREYtpoDTILY63U7Xo5q8bLWZQoXefJnuBZQSZg_ugJR96uj0cgupsR8ScL1IdOdKN9b5jDZ_J5nOChgkmE4OqQ_FgKsvKRpKIljYz63AI5Sd5FfeyhXpPEs9ds18",
      "n": "l0aANg9YywWCxC_t1pgzA4n_DpRHRJIwBlmCrKKd-HUlhFfocWEV7x9NOqk6A_z9yLpmQINMMjBCGffl6sVjbqYG7azdMfTZ4Nolx1oryR2Qk7AmGtxIznil1YbnFQ7T5IOh2tNUDH-uh2Nbo580nsPHcRMBSd49OTlv6p6RIheDAxrEKKfKLgMM9xcE1a3aj9lM7N7Z3FRHKnLQwvnJT1HUHUXloxU872TrLvMnxOjuyxDaHVhadw1wM7j-f1PJb7SuCMIGww1uvVA3K5BBE_b6MEY3JHlXw3gBdrizotlI4WMbhbFqcI1VgAOKmZ-KX7-GPnunP4jadlWbLLBo1Q"
    }
  ]
}