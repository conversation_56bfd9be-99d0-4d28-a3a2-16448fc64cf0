import {
  Adapter,
  AdapterPayload,
  ClientMetadata,
  Configuration,
  FindAccount,
} from "oidc-provider";
import { jwks } from "./jwks.ts";
import { cookies } from "./cookies.ts";
import dotenv from "dotenv";

// NOTE: The default implementations of all configurations can be found here:
// https://github.com/panva/node-oidc-provider/blob/3d1dfe4eba3bb56e65b3e0f7a4321ec613e3a074/lib/helpers/defaults.js#L411
dotenv.config();
dotenv.config({ path: `.env.local`, override: true });

export class Neo4jOidcAdapter implements Adapter {
  upsert(
    id: string,
    payload: AdapterPayload,
    expiresIn: number
  ): Promise<undefined | void> {
    throw new Error("Method not implemented.");
  }
  find(id: string): Promise<AdapterPayload | undefined | void> {
    throw new Error("Method not implemented.");
  }
  findByUserCode(userCode: string): Promise<AdapterPayload | undefined | void> {
    throw new Error("Method not implemented.");
  }
  findByUid(uid: string): Promise<AdapterPayload | undefined | void> {
    throw new Error("Method not implemented.");
  }
  consume(id: string): Promise<undefined | void> {
    throw new Error("Method not implemented.");
  }
  destroy(id: string): Promise<undefined | void> {
    throw new Error("Method not implemented.");
  }
  revokeByGrantId(grantId: string): Promise<undefined | void> {
    throw new Error("Method not implemented.");
  }
}

export const configuration: Configuration = {
  //adapter: Neo4jOidcAdapter,
  clients: getClients(),
  pkce: {
    required: () => true,
    methods: ["S256"],
  },
  claims: {
    acr: null,
    auth_time: null,
    iss: null,
    openid: ["sub", "name", "email"],
    sid: null,
  },
  ttl: {
    // should come from environment variables
    AccessToken: 12 * 60 * 60, // 12 hours
    AuthorizationCode: 10 * 60, // 10 minutes
    IdToken: 1 * 60 * 60, // 1 hour
    DeviceCode: 10 * 60, // 10 minutes
    RefreshToken: 15 * 12 * 60 * 60, // 15 days
    Interaction: 1 * 60 * 60, // 1 hour
    Grant: 1 * 60 * 60, // 1 hour
    Session: 1 * 60 * 60, // 1 hour
  },
  jwks,
  cookies,
  issueRefreshToken(ctx, client, code) {
    return (
      client.grantTypeAllowed("refresh_token") &&
      code.scopes.has("offline_access")
    );
  },
  features: {
    devInteractions: { enabled: false }, // defaults to true
  },
  interactions: {
    url: (context, interaction) => `/interaction/${interaction.uid}`,
  },
  clientBasedCORS(ctx, origin, client) {
    // return true if the origin is allowed
    console.log("clientBasedCORS", ctx, origin, client);
    return true;
  },
  findAccount: findResourceOwner(),
  // renderError: async (ctx, out, error) => {
  //   console.error('Error from OIDC Provider', out)
  //   console.error('Error from OIDC Provider', error)
  //   ctx.type = 'html';
  //   ctx.body = `
  //     <html>
  //       <head>
  //         <title>OIDC Provider Error</title>
  //       </head>
  //       <body>
  //         <h1>OIDC Provider Error</h1>
  //         <p>${error.message}</p>
  //         <pre>${JSON.stringify(out, null, 2)}</pre>
  //       </body>
  //     </html>
  //   `;
  // },
  rotateRefreshToken: async (ctx) => {
    const { RefreshToken: refreshToken, Client: client } = ctx.oidc.entities;
    if (!refreshToken) {
      return false;
    }
    // cap the maximum amount of time a refresh token can be
    // rotated for up to 1 year, afterwards its TTL is final
    if (refreshToken && refreshToken.totalLifetime() >= 365.25 * 24 * 60 * 60) {
      return false;
    }

    // rotate non sender-constrained public client refresh tokens
    if (
      client &&
      refreshToken &&
      client.clientAuthMethod === "none" &&
      !refreshToken.isSenderConstrained()
    ) {
      return true;
    }

    // rotate if the token is nearing expiration (it's beyond 70% of its lifetime)
    return refreshToken.ttlPercentagePassed() >= 70;
  },
};

//TODO is this the default implementation, flesh it out once database is available
function findResourceOwner(): FindAccount | undefined {
  return async (ctx, sub, token) => {
    const req = ctx.request;
    console.log("findResourceOwner", sub, token);
    // TODO: the sub is the email address, we need to find the user in the database
    return {
      accountId: sub,
      async claims(use, scope) {
        return {
          sub,
          name: "Scott Reed", // TODO: find from DB
          email: sub,
        };
      },
    };
  };
}

function getClients(): ClientMetadata[] {
  const redirect_uris = process.env.REDIRECT_URIS?.split(",") || [];

  return [
    {
      client_id: process.env.CLIENT_ID || "",
      client_secret: process.env.CLIENT_SECRET || "",
      grant_types: ["authorization_code"],
      redirect_uris,
      response_types: ["code"],
    },
  ];
}
