/* Define your theme variables */
:root {
    --mdc-theme-primary: #6200ee;
    --mdc-theme-secondary: #03dac6;
    --mdc-theme-background: #ffffff;
    /* Add more theme variables as needed */
  }
  
  /* Import SMUI styles */
  @use '@smui/theme' with (
    $theme: (
      primary: var(--mdc-theme-primary),
      secondary: var(--mdc-theme-secondary),
      background: var(--mdc-theme-background),
    )
  );
  
  @import '@smui/button/styles';
  @import '@smui/card/styles';
  @import '@smui/textfield/styles';
  