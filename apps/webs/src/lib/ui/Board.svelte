<script lang="ts">
    import Column from "./Column.svelte";
    import { writable } from "svelte/store";
    let columns = writable([{ id: 0 }]);

    const addColumn = () => {
        columns.update((currentColumns) => {
            const newId = currentColumns.length;
            return [...currentColumns, { id: newId }];
        });
    };
</script>

<div class="board">
    <div class="content">
        {#each $columns as kard (kard.id)}
            <Column id={kard.id} />
        {/each}
    </div>
    <button onclick={addColumn} class="add-column-button"> Add Column </button>
</div>

<style>
    .board {
        display: flex;
        flex-direction: row;
        justify-content: center;
        width: 100%;
        height: 100%;
        background-color: azure;
    }

    .content {
        display: flex;
        flex-direction: row;
        gap: 10px;
        flex: 1;
    }

    .add-column-button {
        align-self: flex-start;
        margin-left: 10px;
    }
</style>
