<script lang="ts">
    import ColumnFooter from "./ColumnFooter.svelte";
    import ColumnHeader from "./ColumnHeader.svelte";
    import Kard from "./Kard.svelte";
    import { writable } from "svelte/store";
    // Reactive variable to store the list of Kard components
    let kards = writable([{ id: 0 }]);
    let {id} = $props();
    // Function to add a new Kard
    const addCard = () => {
        kards.update((currentKards) => {
            const newId = currentKards.length;
            return [...currentKards, { id: newId }];
        });
    };
</script>

<div class="column">
    <!-- Good: clean string interpolation -->
    <ColumnHeader header="Column Header {id}" />
    <button class="add-card-button" onclick={addCard}> Add Card </button>
    <div class="content">
        {#each $kards as kard (kard.id)}
            <Kard id={kard.id} />
        {/each}
    </div>
    <ColumnFooter footer="Column Footer {id}" />
</div>

<style>
    .column {
        width: 20%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        background-color: aliceblue;
    }

    .content {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8px;
    }
    .add-card-button {
        align-self: flex-start;
        margin-left: 10px;
    }
</style>
