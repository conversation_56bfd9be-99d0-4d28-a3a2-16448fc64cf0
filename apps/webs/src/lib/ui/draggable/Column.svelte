<script lang="ts">
    import Card from "./Card.svelte";
    import type { Card as CardType } from "$lib/types";

    export let title: string;
    export let cards: CardType[] = [];
    export let width: string = "300px";

    // Callback when a card is dropped into this column
    export let onCardDrop: () => void;

    // Callbacks for card drag events
    export let onDragStart: (e: DragEvent, card: CardType) => void;
    export let onDragEnd: (e: DragEvent) => void;

    // Callback for when the column is resized
    export let onResize: (newWidth: string) => void;

    let startX = 0;
    let startWidth = 0;

    function handleDragOver(e: DragEvent) {
        // To allow dropping, prevent the default behavior
        e.preventDefault();
    }

    function handleDrop(e: DragEvent) {
        e.preventDefault();
        onCardDrop();
    }

    function onMouseDown(e: MouseEvent) {
        startX = e.clientX;
        startWidth = parseFloat(width);
        window.addEventListener("mousemove", onMouseMove);
        window.addEventListener("mouseup", onMouseUp);
    }

    function onMouseMove(e: MouseEvent) {
        const dx = e.clientX - startX;
        const newWidth = startWidth + dx;
        if (newWidth > 150) {
            // minimum width
            onResize(newWidth + "px");
        }
    }

    function onMouseUp() {
        window.removeEventListener("mousemove", onMouseMove);
        window.removeEventListener("mouseup", onMouseUp);
    }
</script>

<div
    class="column"
    style={`width: ${width}`}
    on:dragover={handleDragOver}
    on:drop={handleDrop}
>
    <div class="column-header">
        <h3>{title}</h3>
    </div>

    <div class="column-cards">
        {#each cards as card}
            <Card {card} {onDragStart} {onDragEnd} />
        {/each}
    </div>

    <div class="resize-handle" on:mousedown={onMouseDown}></div>
</div>

<style>
    .column {
        background: #f5f5f5;
        border: 1px solid #ddd;
        display: flex;
        flex-direction: column;
        position: relative;
        margin: 0 4px;
    }
    .column-header {
        padding: 8px;
        border-bottom: 1px solid #ccc;
        background: #eee;
    }
    .column-cards {
        flex: 1;
        padding: 8px;
        overflow-y: auto;
    }
    .resize-handle {
        width: 5px;
        cursor: col-resize;
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        background: #ccc;
    }
</style>
