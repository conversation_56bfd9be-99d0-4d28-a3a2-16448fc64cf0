<script lang="ts">
    import type { Card } from "$lib/types";

    export let card: Card;
    export let onDragStart: (e: DragEvent, card: Card) => void;
    export let onDragEnd: (e: DragEvent) => void;
</script>

<div
    class="card"
    draggable="true"
    on:dragstart={(e) => onDragStart(e, card)}
    on:dragend={(e) => onDragEnd(e)}
>
    {card.title}
</div>

<style>
    .card {
        background: #fff;
        padding: 8px;
        margin: 8px 0;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        cursor: grab;
    }
    .card:active {
        cursor: grabbing;
    }
</style>
