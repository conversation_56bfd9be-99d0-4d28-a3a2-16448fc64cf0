<script lang="ts">
    import Column from "./Column.svelte";
    import type { Column as ColumnType, Card } from "$lib/types";

    let columns: ColumnType[] = [
        {
            id: "col-1",
            title: "To Do",
            width: "300px",
            cards: [
                { id: "card-1", title: "Task A" },
                { id: "card-2", title: "Task B" },
            ],
        },
        {
            id: "col-2",
            title: "In Progress",
            width: "300px",
            cards: [{ id: "card-3", title: "Task C" }],
        },
        {
            id: "col-3",
            title: "Done",
            width: "300px",
            cards: [],
        },
    ];

    let draggedCard: Card | null = null;
    let sourceColumnId: string | null = null;

    function onDragStart(e: DragEvent, card: Card) {
        draggedCard = card;
        const col = columns.find((col) =>
            col.cards.some((c) => c.id === card.id),
        );
        sourceColumnId = col ? col.id : null;
        e.dataTransfer?.setData("text/plain", card.id);
        e.dataTransfer!.effectAllowed = "move";
    }

    function onDragEnd(_e: DragEvent) {
        draggedCard = null;
        sourceColumnId = null;
    }

    function onCardDrop(targetColumnId: string) {
        if (!draggedCard || !sourceColumnId) return;

        // Remove card from source column
        columns = columns.map((col) => {
            if (col.id === sourceColumnId) {
                return {
                    ...col,
                    cards: col.cards.filter((c) => c.id !== draggedCard!.id),
                };
            }
            return col;
        });

        // Add card to target column
        columns = columns.map((col) => {
            if (col.id === targetColumnId) {
                return { ...col, cards: [...col.cards, draggedCard!] };
            }
            return col;
        });

        draggedCard = null;
        sourceColumnId = null;
    }

    function onResizeColumn(columnId: string, newWidth: string) {
        columns = columns.map((col) => {
            if (col.id === columnId) {
                return { ...col, width: newWidth };
            }
            return col;
        });
    }
</script>

<div class="board">
    {#each columns as column}
        <Column
            title={column.title}
            width={column.width}
            cards={column.cards}
            onCardDrop={() => onCardDrop(column.id)}
            {onDragStart}
            {onDragEnd}
            onResize={(w) => onResizeColumn(column.id, w)}
        />
    {/each}
</div>

<style>
    .board {
        display: flex;
        flex-direction: row;
        height: 100vh;
        overflow-x: auto;
    }
</style>
