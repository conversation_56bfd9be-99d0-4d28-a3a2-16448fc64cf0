{"name": "webs", "version": "0.0.1", "scripts": {"dev": "vite dev --port 3300 --host", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "prepare": "npm run smui-theme-light && npm run smui-theme-dark", "smui-theme-light": "smui-theme compile static/smui.css -i src/theme", "smui-theme-dark": "smui-theme compile static/smui-dark.css -i src/theme/dark"}, "devDependencies": {"@fontsource/fira-mono": "^5.0.0", "@neoconfetti/svelte": "^2.0.0", "@sveltejs/adapter-auto": "^3.0.0", "@sveltejs/kit": "^2.0.0", "@sveltejs/vite-plugin-svelte": "^4.0.0-next.6", "svelte": "^5.0.0-next.1", "svelte-check": "^3.6.0", "typescript": "^5.0.0", "vite": "^5.0.3"}, "type": "module", "dependencies": {"@auth/sveltekit": "^1.4.2", "@opentelemetry/exporter-metrics-otlp-grpc": "^0.53.0", "@opentelemetry/instrumentation-express": "^0.43.0", "@opentelemetry/instrumentation-http": "^0.53.0", "@opentelemetry/instrumentation-redis-4": "^0.42.1", "@opentelemetry/metrics": "^0.24.0", "@opentelemetry/sdk-node": "^0.53.0", "@opentelemetry/tracing": "^0.24.0", "@smui/button": "^8.0.0-beta.0", "@smui/card": "^8.0.0-beta.0", "@smui/textfield": "^8.0.0-beta.0", "smui-theme": "^8.0.0-beta.0"}}