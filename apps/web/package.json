{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.13.0", "@emotion/cache": "^11.13.1", "@emotion/styled": "^11.13.0", "@mui/material-nextjs": "^6.0.1", "@repo/ui": "*", "dotenv": "^16.4.5", "next": "15.0.0-rc.0", "next-auth": "beta", "react": "19.0.0-rc-f994737d14-20240522", "react-dom": "19.0.0-rc-f994737d14-20240522"}, "devDependencies": {"@repo/eslint-config": "*", "@repo/typescript-config": "*", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "^14.2.5", "next-transpile-modules": "^10.0.1", "typescript": "^5"}}