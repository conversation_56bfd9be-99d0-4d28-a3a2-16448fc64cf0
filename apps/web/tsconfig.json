{
  "extends": "@repo/typescript-config/nextjs.json",
  "compilerOptions": {
    "declaration": false,
    "declarationMap": false
    // "plugins": [
    //   {
    //     "name": "next"
    //   }
    // ],
    // "paths": {
    //   "@mui/styled-engine": ["./node_modules/@mui/styled-engine-sc"]
    // }
  },
  "include": [
    "next-env.d.ts",
    "next.config.mjs",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts"
  ],
  "exclude": ["node_modules"]
}
