import { Box } from "@mui/material";
import { auth } from "../../auth";
import SignIn from "../ui/sign-in";
import UserAvatar from "./user-avatar";
import SignOut from "./sign-out";

export async function AppHeader() {
  const session = await auth();

  return (
    <Box className="navbar" color="primary.contrastText" bgcolor="primary.dark">
      {!session?.user && <SignIn />}
      {session?.user && (
        <>
          <UserAvatar />
          <SignOut />
        </>
      )}
    </Box>
  );
}
