import NextAuth from "next-auth";
//import Google from "next-auth/providers/google";

export const { handlers, signIn, signOut, auth } = NextAuth({
  providers: [
    {
      id: "node-provider", // signIn("my-provider") and will be part of the callback URL
      name: "Node Provider", // optional, used on the default login page as the button text.
      type: "oidc", // or "oauth" for OAuth 2 providers
      authorization: { params: { scope: "openid email profile" } },
      issuer: "http://localhost:4000/oidc", // to infer the .well-known/openid-configuration URL
      clientId: process.env.AUTH_CLIENT_ID, // from the provider's dashboard
      clientSecret: process.env.AUTH_CLIENT_SECRET, // from the provider's dashboard
    },
  ],
});
