"use client";
import { <PERSON><PERSON>, <PERSON>ack } from "@mui/material";
import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { SortableContext, arrayMove } from "@dnd-kit/sortable";
import { createPortal } from "react-dom";
import { useEffect, useMemo, useRef, useState } from "react";
import { Id } from "@repo/shared-types/Id";
import { Column } from "@repo/shared-types/Column";
import { Card } from "@repo/shared-types/Card";
import { KanbanColumn } from "./KanbanColumn";
import { BoardHeader } from "./BoardHeader";
import { BoardFooter } from "./BoardFooter";
import { KanbanCard } from "./KanbanCard";

export function KanbanBoard() {
  const [columns, setColumns] = useState<Column[]>([]);
  const [cards, setCards] = useState<Card[]>([]);

  const columnIds = useMemo(
    () => columns.map((column) => column.id),
    [columns]
  );
  const [columnBeingDragged, setColumnBeingDragged] = useState<Column | null>(
    null
  );
  const [cardBeingDragged, setCardBeingDragged] = useState<Card | null>(null);

  const isMountedRef = useRef(false);
  useEffect(() => {
    isMountedRef.current = true;
  }, []);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 3, // pixels
      },
    })
  );

  const generateId = () => Math.floor(Math.random() * 10000);

  function createColumn() {
    const newColumn: Column = {
      id: generateId(),
      title: "",
    };
    newColumn.title = `New Column ${newColumn.id}`;
    setColumns([...columns, newColumn]);
  }

  function updateColumnTitle(columnId: Id, newTitle: string) {
    const updatedColumns = columns.map((column) => {
      if (column.id === columnId) {
        return { ...column, title: newTitle };
      }
      return column;
    });
    setColumns(updatedColumns);
  }

  function deleteColumn(columnId: Id) {
    const updatedColumns = columns.filter((column) => column.id !== columnId);
    setColumns(updatedColumns);
    const updatedCards = cards.filter((card) => card.columnId !== columnId);
    setCards(updatedCards);
  }

  function createCard(columnId: Id) {
    const newCard: Card = {
      id: generateId(),
      title: "",
      columnId: columnId,
    };
    newCard.title = `New Card ${newCard.id}`;
    setCards([...cards, newCard]);
  }

  function updateCardTitle(cardId: Id, newTitle: string) {
    const updatedCards = cards.map((card) => {
      if (card.id === cardId) {
        return { ...card, title: newTitle };
      }
      return card;
    });
    setCards(updatedCards);
  }

  function deleteCard(cardId: Id) {
    const updatedCards = cards.filter((card) => card.id !== cardId);
    setCards(updatedCards);
  }

  function handleDragStart(event: DragStartEvent) {
    if (event.active.data.current?.type === "column") {
      const column = event.active.data.current.column as Column;
      setColumnBeingDragged(column);
    }
    if (event.active.data.current?.type === "card") {
      const card = event.active.data.current.card as Card;
      setCardBeingDragged(card);
    }
  }

  function handleDragEnd(event: DragEndEvent) {
    setColumnBeingDragged(null);
    setCardBeingDragged(null);
    const { active, over } = event;
    if (!over || active.id === over.id) {
      return;
    }
    const activeIndex = columns.findIndex((col) => col.id == active.id);
    const overIndex = columns.findIndex((col) => col.id == over.id);
    setColumns(arrayMove(columns, activeIndex, overIndex));
  }

  function handleDragOver(event: DragOverEvent) {
    const { active, over } = event;
    if (!over || active.id === over.id) {
      return;
    }
    const isActiveCard = active.data.current?.type === "card";
    if (!isActiveCard) {
      return;
    }
    const activeIndex = cards.findIndex((card) => card.id == active.id);

    const isOverCard = over.data.current?.type === "card";
    // dropping a card over another card
    if (isOverCard) {
      const overIndex = cards.findIndex((card) => card.id == over.id);
      cards[activeIndex].columnId = cards[overIndex].columnId;
      setCards(arrayMove(cards, activeIndex, overIndex));
    }

    // dropping a card over a column
    const isOverColumn = over.data.current?.type === "column";
    if (isOverColumn) {
      cards[activeIndex].columnId = over.id;
      setCards(arrayMove(cards, activeIndex, activeIndex));
    }
  }

  return (
    <Stack sx={{ display: "flex", flex: "1 1 auto", height: "100%" }}>
      <BoardHeader />

      <Stack
        className="kanban-container"
        direction="row"
        gap={2}
        sx={{ display: "flex", flex: "1 1 auto", height: "100%" }}
      >
        <DndContext
          sensors={sensors}
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
          onDragOver={handleDragOver}
        >
          <SortableContext items={columnIds}>
            {columns.map((column) => {
              return (
                <KanbanColumn
                  key={column.id}
                  column={column}
                  onUpdateColumnTitle={updateColumnTitle}
                  onDeleteColumn={deleteColumn}
                  cards={cards.filter((card) => card.columnId === column.id)}
                  onCreateCard={createCard}
                  onUpdateCardTitle={updateCardTitle}
                  onDeleteCard={deleteCard}
                />
              );
            })}
          </SortableContext>
          <Stack>
            <Button onClick={createColumn}>Add Column</Button>
          </Stack>
          {isMountedRef.current && (
            <>
              {createPortal(
                <DragOverlay>
                  {columnBeingDragged && (
                    // TODO: these are really just overlay's we don't have to use the real component
                    <KanbanColumn
                      column={columnBeingDragged}
                      onUpdateColumnTitle={updateColumnTitle}
                      onDeleteColumn={deleteColumn}
                      cards={cards.filter(
                        (card) => card.columnId === columnBeingDragged.id
                      )}
                      onCreateCard={createCard}
                      onUpdateCardTitle={updateCardTitle}
                      onDeleteCard={deleteCard}
                    />
                  )}
                  {cardBeingDragged && (
                    <KanbanCard
                      card={cardBeingDragged}
                      onUpdateTitle={updateCardTitle}
                      onDelete={deleteCard}
                    />
                  )}
                </DragOverlay>,
                document.body
              )}
            </>
          )}
        </DndContext>
      </Stack>
      <BoardFooter />
    </Stack>
  );
}
