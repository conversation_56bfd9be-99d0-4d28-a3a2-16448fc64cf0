"use client";
import { useState } from "react";
import { <PERSON>, <PERSON>ack, TextField, Typography } from "@mui/material";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

import { Card } from "@repo/shared-types/Card";
import { Id } from "@repo/shared-types/Id";

import { CardHeader } from "./CardHeader";
import { CardFooter } from "./CardFooter";

interface KanbanCardProps {
  card: Card;
  onDelete: (cardId: Id) => void;
  onUpdateTitle: (cardId: Id, newTitle: string) => void;
}

export function KanbanCard({ card, onDelete, onUpdateTitle }: KanbanCardProps) {
  const [isMouseOver, setIsMouseOver] = useState(false);
  const [isEditMode, setIsEditMode] = useState(true);

  const {
    setNodeRef,
    attributes,
    listeners,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: card.id,
    data: { type: "card", card },
    disabled: isEditMode,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  function handleClick() {
    setIsEditMode(true);
    setIsMouseOver(false);
  }

  function handleEditBlur() {
    setIsEditMode(false);
  }

  function handleEditKeyDown(e: React.KeyboardEvent<HTMLDivElement>) {
    if (e.key == "Enter" && !e.shiftKey) {
      setIsEditMode(false);
    }
  }

  if (isDragging) {
    return (
      <Box
        ref={setNodeRef}
        style={style}
        border={1}
        bgcolor="white"
        minHeight="100px"
        sx={{ opacity: "40%" }}
      ></Box>
    );
  }

  return (
    <Stack
      bgcolor="white"
      minHeight="100px"
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      sx={{
        "&:hover": {
          border: "1px solid black",
        },
      }}
      onClick={handleClick}
      onMouseEnter={() => {
        setIsMouseOver(true);
      }}
      onMouseLeave={() => {
        setIsMouseOver(false);
      }}
    >
      <CardHeader
        cardId={card.id}
        onDelete={onDelete}
        shouldShowDelete={!isEditMode && isMouseOver}
      />
      {isEditMode && (
        <TextField
          autoFocus
          multiline
          onBlur={handleEditBlur}
          onChange={(e) => onUpdateTitle(card.id, e.target.value)}
          onKeyDown={handleEditKeyDown}
          value={card.title}
        />
      )}
      {!isEditMode && (
        <Typography sx={{ flex: 1, whiteSpace: "pre-wrap" }}>
          {card.title}
        </Typography>
      )}
      <CardFooter />
    </Stack>
  );
}
