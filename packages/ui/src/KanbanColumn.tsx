"use client";
import { useMemo, useState } from "react";
import { SortableContext, useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Box, Stack } from "@mui/material";
import { Column } from "@repo/shared-types/Column";
import { Id } from "@repo/shared-types/Id";
import { ColumnHeader } from "./ColumnHeader";
import { ColumnFooter } from "./ColumnFooter";
import { Card } from "@repo/shared-types/Card";
import { KanbanCard } from "./KanbanCard";

interface KanbanColumnProps {
  column: Column;
  onDeleteColumn: (columnId: Id) => void;
  onUpdateColumnTitle: (columnId: Id, newTitle: string) => void;

  cards: Card[];
  onCreateCard: (columnId: Id) => void;
  onUpdateCardTitle: (cardId: Id, newTitle: string) => void;
  onDeleteCard: (cardId: Id) => void;
}

export function KanbanColumn({
  column,
  onDeleteColumn,
  onUpdateColumnTitle,
  onCreateCard,
  onUpdateCardTitle,
  onDeleteCard,
  cards,
}: KanbanColumnProps) {
  const [isEditMode, setIsEditMode] = useState(false);

  const {
    setNodeRef,
    attributes,
    listeners,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: column.id,
    data: { type: "column", column },
    disabled: isEditMode,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const cardIds = useMemo(() => cards.map((card) => card.id), [cards]);

  if (isDragging) {
    return (
      <Box
        ref={setNodeRef}
        style={style}
        border={1}
        bgcolor="secondary.light"
        width="350px"
        sx={{ opacity: "40%" }}
      ></Box>
    );
  }

  return (
    <Stack
      ref={setNodeRef}
      style={style}
      bgcolor="secondary.light"
      width="350px"
      className="kanban-column"
      sx={{ display: "flex", flexDirection: "column", height: "100%" }}
    >
      <ColumnHeader
        column={column}
        isEditMode={isEditMode}
        dndAttributes={attributes}
        dndListeners={listeners}
        setIsEditMode={setIsEditMode}
        onDelete={onDeleteColumn}
        onUpdateTitle={onUpdateColumnTitle}
      />
      <Stack
        className="column-card-list"
        padding={1}
        spacing={1}
        flexGrow={1}
        sx={{ flex: 1, overflowY: "auto" }}
      >
        <SortableContext items={cardIds}>
          {cards.map((card) => (
            <KanbanCard
              key={card.id}
              card={card}
              onUpdateTitle={onUpdateCardTitle}
              onDelete={onDeleteCard}
            />
          ))}
        </SortableContext>
      </Stack>
      <ColumnFooter columnId={column.id} onCreateCard={onCreateCard} />
    </Stack>
  );
}
