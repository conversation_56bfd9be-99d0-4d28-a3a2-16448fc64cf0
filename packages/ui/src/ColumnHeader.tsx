import { Box, Button, Stack, TextField } from "@mui/material";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import { DraggableAttributes } from "@dnd-kit/core";
import { SyntheticListenerMap } from "@dnd-kit/core/dist/hooks/utilities";
import { Column } from "@repo/shared-types/Column";
import { Id } from "@repo/shared-types/Id";

interface ColumnHeaderProps {
  column: Column;
  isEditMode: boolean;
  onDelete: (columnId: Id) => void;
  onUpdateTitle: (columnId: Id, newTitle: string) => void;
  dndAttributes: DraggableAttributes;
  dndListeners: SyntheticListenerMap | undefined;
  setIsEditMode: (isEditMode: boolean) => void;
}

export function ColumnHeader({
  column,
  isEditMode,
  onDelete,
  onUpdateTitle,
  dndAttributes,
  dndListeners,
  setIsEditMode,
}: ColumnHeaderProps) {
  function handleHeaderClick() {
    setIsEditMode(true);
  }

  function handleEditBlur() {
    setIsEditMode(false);
  }

  function handleEditKeyDown(e: React.KeyboardEvent<HTMLDivElement>) {
    if (e.key !== "Enter") return;
    setIsEditMode(false);
  }

  return (
    <Stack
      bgcolor="primary.light"
      className="column-header"
      color="primary.contrastText"
      direction="row"
      gap={2}
      justifyContent="space-between"
      {...dndAttributes}
      {...dndListeners}
      onClick={handleHeaderClick}
      sx={{ display: "flex", flexDirection: "row", width: "100%" }}
    >
      <Box sx={{ py: 1, px: 2 }}>0</Box>
      <Box flexGrow={1} sx={{ flex: 1, py: 1, px: 2, width: "100%" }}>
        {!isEditMode && column.title}
        {isEditMode && (
          <TextField
            autoFocus
            onBlur={handleEditBlur}
            onChange={(e) => onUpdateTitle(column.id, e.target.value)}
            onKeyDown={handleEditKeyDown}
            value={column.title}
            sx={{ width: "100%" }}
          />
        )}
      </Box>
      <Button sx={{ minWidth: "36px" }} onClick={() => onDelete(column.id)}>
        <DeleteOutlineOutlinedIcon />
      </Button>
    </Stack>
  );
}
