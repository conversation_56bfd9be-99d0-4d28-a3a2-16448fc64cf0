import { Button, Stack } from "@mui/material";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import DragHandleIcon from "@mui/icons-material/DragHandle";
import { Id } from "@repo/shared-types/Id";

interface CardHeaderProps {
  cardId: Id;
  shouldShowDelete: boolean;
  onDelete: (cardId: Id) => void;
}

export function CardHeader({
  cardId,
  shouldShowDelete,
  onDelete,
}: CardHeaderProps) {
  return (
    <Stack
      color="primary.contrastText"
      bgcolor="primary.main"
      direction="row"
      justifyContent="space-between"
      maxHeight="12px"
      sx={{ borderRadius: "6px 6px 0 0" }}
    >
      <DragHandleIcon
        sx={{ color: "white", maxHeight: "12px", maxWidth: "12px" }}
      />
      {shouldShowDelete && (
        <Button
          sx={{ color: "white", maxHeight: "12px", maxWidth: "12px" }}
          onClick={() => onDelete(cardId)}
        >
          <DeleteOutlineOutlinedIcon />
        </Button>
      )}
    </Stack>
  );
}
