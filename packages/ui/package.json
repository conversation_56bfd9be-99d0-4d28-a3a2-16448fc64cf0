{"name": "@repo/ui", "version": "0.0.0", "private": true, "exports": {"./BoardFooter": "./src/BoardFooter.tsx", "./BoardHeader": "./src/BoardHeader.tsx", "./CardFooter": "./src/CardFooter.tsx", "./CardHeader": "./src/CardHeader.tsx", "./ColumnFooter": "./src/ColumnFooter.tsx", "./ColumnHeader": "./src/ColumnHeader.tsx", "./KanbanBoard": "./src/KanbanBoard.tsx", "./KanbanCard": "./src/KanbanCard.tsx", "./KanbanColumn": "./src/KanbanColumn.tsx"}, "scripts": {"lint": "eslint . --max-warnings 0", "generate:component": "turbo gen react-component"}, "devDependencies": {"@repo/eslint-config": "*", "@repo/typescript-config": "*", "@repo/shared-types": "*", "@turbo/gen": "^1.12.4", "@types/node": "^20.11.24", "@types/eslint": "^8.56.5", "@types/react": "^18.2.61", "@types/react-dom": "^18.2.19", "eslint": "^8.57.0", "typescript": "^5"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fontsource/roboto": "^5.0.8", "@mui/icons-material": "^5.14.19", "@mui/material": "^5.14.19", "react": "^18.3.1", "react-dom": "18.3.1"}}