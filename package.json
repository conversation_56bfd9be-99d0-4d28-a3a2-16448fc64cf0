{"name": "kanban", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "test": "turbo run test", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "dev-oidc": "turbo run dev --filter=./apps/oidc"}, "devDependencies": {"prettier": "^3.3.3", "turbo": "^2.3.3", "typescript": "^5"}, "engines": {"node": ">=18"}, "packageManager": "bun@1.0.14", "workspaces": ["apps/*", "packages/*"]}