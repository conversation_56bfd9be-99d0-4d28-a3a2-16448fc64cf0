using System.Text;
using Kanban.Integrations.Ado;

namespace Kanban.Integrations.Services;

public static class CypherService
{
  public static string TransformToCypher(string boardUrl, string boardTitle, WorkItemModel[] workItems)
  {
    var cypher = new StringBuilder();
    foreach (var workItem in workItems)
    {
      cypher.AppendLine($"MERGE (i:Import {{ url: '{boardUrl}' }})");
      cypher.AppendLine("ON CREATE SET i.externalId = randomUUID()");
      cypher.AppendLine($"MERGE (i)<-[:IMPORTED_FROM]-(b:Board {{ title: '{boardTitle}' }})");
      cypher.AppendLine("ON CREATE SET b.externalId = randomUUID()");
      cypher.AppendLine($"MERGE (b)<-[:IS_STATE_IN]-(col:Column {{ name: '{workItem.Fields.State}' }})");
      cypher.AppendLine("ON CREATE SET col.externalId = randomUUID()");
      cypher.AppendLine($"MERGE (col)<-[:HAS_STATE]-(c:Card {{ importedId: '{workItem.Id}' }} )");
      cypher.AppendLine("ON CREATE SET");
      cypher.AppendLine("  c.externalId = randomUUID(),");
      OutputSetClause(cypher, workItem);
      cypher.AppendLine("ON MATCH SET");
      OutputSetClause(cypher, workItem);
      cypher.AppendLine(";");
    }

    return cypher.ToString();
  }

  private static void OutputSetClause(StringBuilder cypher, WorkItemModel workItem)
  {
    cypher.AppendLine($"  c.title= '{workItem.Fields.Title}',");
    cypher.AppendLine($"  c.description= '{workItem.Fields.Description}',");
    cypher.AppendLine($"  c.createdBy= '{workItem.Fields.CreatedBy.UserName}',");
    cypher.AppendLine($"  c.createdOn= '{workItem.Fields.CreatedDate.ToString("O")}',");
    cypher.AppendLine($"  c.modifiedBy= '{workItem.Fields.ChangedBy.UserName}',");
    cypher.AppendLine($"  c.modifiedOn= '{workItem.Fields.ChangedDate}'");
    
    // cypher.AppendLine($"Id: {workItem.Id},");
    // cypher.AppendLine($"Rev: {workItem.Rev},");
    // cypher.AppendLine($"Project: '{workItem.Fields.Project}',");
    // cypher.AppendLine($"WorkItemType: '{workItem.Fields.WorkItemType}',");
    // cypher.AppendLine($"Reason: '{workItem.Fields.Reason}',");
    // cypher.AppendLine($"Column: '{workItem.Fields.Column}'");

  }
}
