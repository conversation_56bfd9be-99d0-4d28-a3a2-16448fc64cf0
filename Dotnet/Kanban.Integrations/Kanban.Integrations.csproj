<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <UserSecretsId>909b3249-5a0b-4f52-95da-f2e61c491c3f</UserSecretsId>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.2" />
        <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.2" />
        <PackageReference Include="Microsoft.Extensions.Configuration.UserSecrets" Version="9.0.2" />
    </ItemGroup>

    <ItemGroup>
      <None Update="appsettings.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
    </ItemGroup>

</Project>
