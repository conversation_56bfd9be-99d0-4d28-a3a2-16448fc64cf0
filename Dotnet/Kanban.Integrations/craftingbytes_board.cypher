Data extraction complete.
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'Doing' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '4' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'CB: Kanban of Kanbans',
  c.description= '',
  c.createdBy= '<PERSON><PERSON>',
  c.createdOn= '1/25/2024 11:06:20 PM',
  c.modifiedBy= '<PERSON><PERSON>',
  c.modifiedOn= '7/21/2025 6:24:59 PM'
ON MATCH SET
  c.title= 'CB: <PERSON><PERSON><PERSON> of Kanbans',
  c.description= '',
  c.createdBy= '<PERSON><PERSON>',
  c.createdOn= '1/25/2024 11:06:20 PM',
  c.modifiedBy= '<PERSON><PERSON>',
  c.modifiedOn= '7/21/2025 6:24:59 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'Doing' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '18' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'Techmer Solutions',
  c.description= '<div>Building a synapse/power bi data platform. ; Rescuing a poorly run KPMG project. </div><div>
 </div><div>Might need two fulltime people. </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '7/15/2024 6:42:45 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/21/2025 6:23:16 PM'
ON MATCH SET
  c.title= 'Techmer Solutions',
  c.description= '<div>Building a synapse/power bi data platform. ; Rescuing a poorly run KPMG project. </div><div>
 </div><div>Might need two fulltime people. </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '7/15/2024 6:42:45 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/21/2025 6:23:16 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'Doing' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '28' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'Xantie: Data recovery from Dynamics for a law firm/bankruptcy case',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/14/2025 3:50:35 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/21/2025 6:20:44 PM'
ON MATCH SET
  c.title= 'Xantie: Data recovery from Dynamics for a law firm/bankruptcy case',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/14/2025 3:50:35 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/21/2025 6:20:44 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'Doing' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '10' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'SWLaw: Initiative Petitions',
  c.description= '<div>First part is just getting the boundary checking to work correctly. </div><div>
The second part has two subprojects: </div><div><ul><li>Data pipeline and data platform </li><li>Web/ App changes for looking at output as a set of static images and for looking at outliers that didn\'t form recognize very well. </li> </ul><div>
 </div><div>
 </div><div>ADO Board: ; ;<a href="https://swlawappdev.visualstudio.com/SW%20Law%20Signature%20Application%20Phase%202/_boards/board/t/SW%20Law%20Signature%20Application%20Phase%202%20Team/Stories">https://swlawappdev.visualstudio.com/SW%20Law%20Signature%20Application%20Phase%202/_boards/board/t/SW%20Law%20Signature%20Application%20Phase%202%20Team/Stories</a> </div> </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '2/26/2024 7:19:45 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/21/2025 6:20:07 PM'
ON MATCH SET
  c.title= 'SWLaw: Initiative Petitions',
  c.description= '<div>First part is just getting the boundary checking to work correctly. </div><div>
The second part has two subprojects: </div><div><ul><li>Data pipeline and data platform </li><li>Web/ App changes for looking at output as a set of static images and for looking at outliers that didn\'t form recognize very well. </li> </ul><div>
 </div><div>
 </div><div>ADO Board: ; ;<a href="https://swlawappdev.visualstudio.com/SW%20Law%20Signature%20Application%20Phase%202/_boards/board/t/SW%20Law%20Signature%20Application%20Phase%202%20Team/Stories">https://swlawappdev.visualstudio.com/SW%20Law%20Signature%20Application%20Phase%202/_boards/board/t/SW%20Law%20Signature%20Application%20Phase%202%20Team/Stories</a> </div> </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '2/26/2024 7:19:45 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/21/2025 6:20:07 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'Doing' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '5' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'Toppan Merrill: Data Platform Implementation',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '1/25/2024 11:06:32 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/21/2025 6:19:29 PM'
ON MATCH SET
  c.title= 'Toppan Merrill: Data Platform Implementation',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '1/25/2024 11:06:32 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/21/2025 6:19:29 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'Doing' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '25' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'BankJoy: Joysite 360 - Customer FI analytics platform',
  c.description= '<div>New project for Phll </div><div>
 </div><div>Databricks is back on the table. </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '3/31/2025 6:08:51 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/21/2025 6:18:53 PM'
ON MATCH SET
  c.title= 'BankJoy: Joysite 360 - Customer FI analytics platform',
  c.description= '<div>New project for Phll </div><div>
 </div><div>Databricks is back on the table. </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '3/31/2025 6:08:51 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/21/2025 6:18:53 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'Doing' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '55' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'Solliance - JGC - Andrew project',
  c.description= '<div><ul><li>Done identity work </li><li>Have a policy and identity server </li><li>Have an application written using devExpress (old school component library) </li><li>Looking for a modern REACT frontend </li><li>Plug in to existing authentication and authorization system. ; (Auth-N, Auth-Z) </li> </ul> </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '7/21/2025 6:11:21 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/21/2025 6:18:19 PM'
ON MATCH SET
  c.title= 'Solliance - JGC - Andrew project',
  c.description= '<div><ul><li>Done identity work </li><li>Have a policy and identity server </li><li>Have an application written using devExpress (old school component library) </li><li>Looking for a modern REACT frontend </li><li>Plug in to existing authentication and authorization system. ; (Auth-N, Auth-Z) </li> </ul> </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '7/21/2025 6:11:21 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/21/2025 6:18:19 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'To Do' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '52' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'Solliance - baseline',
  c.description= 'Company George Kelly Howell and Steven S. ; ;

<div>They have an opportunity with Delloitte. ;  ;Possibly a million dollar SOW.
 </div>
Product:

<div><ul><li>Ingests microfacts </li><li>Assesses credibility and reputation </li><li>Summarizes key points for executives of specific companies </li><li>Timelines </li><li>Builds different infographics and visuals and reports ; </li> </ul> </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '7/14/2025 6:15:02 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/21/2025 6:10:39 PM'
ON MATCH SET
  c.title= 'Solliance - baseline',
  c.description= 'Company George Kelly Howell and Steven S. ; ;

<div>They have an opportunity with Delloitte. ;  ;Possibly a million dollar SOW.
 </div>
Product:

<div><ul><li>Ingests microfacts </li><li>Assesses credibility and reputation </li><li>Summarizes key points for executives of specific companies </li><li>Timelines </li><li>Builds different infographics and visuals and reports ; </li> </ul> </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '7/14/2025 6:15:02 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/21/2025 6:10:39 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'To Do' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '24' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'Identifee - Unity Catalog Implementation',
  c.description= '<div><ul><li>AWS Client </li><li>Databricks </li><li>Need fast data engineering for a fintech product for commercial banking </li><li>Dealing with the CEO and CTO </li> </ul> </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '3/17/2025 6:09:10 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/21/2025 6:09:50 PM'
ON MATCH SET
  c.title= 'Identifee - Unity Catalog Implementation',
  c.description= '<div><ul><li>AWS Client </li><li>Databricks </li><li>Need fast data engineering for a fintech product for commercial banking </li><li>Dealing with the CEO and CTO </li> </ul> </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '3/17/2025 6:09:10 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/21/2025 6:09:50 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'To Do' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '54' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'Solliance - Databricks Technical Conference',
  c.description= '<div>Conference in Orlando </div><div>Precons Oct 5,6,10 </div><div>Conference Oct 7 - 9 </div><div>
 </div><div>Simon Whitely </div><div>Dustin Vannoy </div><div>
 </div><div>Our goal is to sell 500 tickets </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '7/21/2025 6:05:28 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/21/2025 6:09:41 PM'
ON MATCH SET
  c.title= 'Solliance - Databricks Technical Conference',
  c.description= '<div>Conference in Orlando </div><div>Precons Oct 5,6,10 </div><div>Conference Oct 7 - 9 </div><div>
 </div><div>Simon Whitely </div><div>Dustin Vannoy </div><div>
 </div><div>Our goal is to sell 500 tickets </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '7/21/2025 6:05:28 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/21/2025 6:09:41 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'To Do' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '47' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'Solliance Data & AI Practice - Michele',
  c.description= '<div>General data architecture and data leadership. </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '5/12/2025 6:14:26 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/21/2025 6:05:14 PM'
ON MATCH SET
  c.title= 'Solliance Data & AI Practice - Michele',
  c.description= '<div>General data architecture and data leadership. </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '5/12/2025 6:14:26 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/21/2025 6:05:14 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'To Do' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '32' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'NCD Outsourced Data Leader - Jackie Pounds',
  c.description= '<div><img src="https://dev.azure.com/ike0642/bff46545-7652-411a-ad0f-14e800efe36a/_apis/wit/attachments/dbd94026-18f3-425f-8927-8690e8e325b4?fileName=image.png" alt=Image>
 </div><div><div style="box-sizing:border-box;margin:0px;"><span style="box-sizing:border-box;">• Trying to create a data warehouse (google big query, DBT, and Omni BI)</span> </div><div style="box-sizing:border-box;margin:0px;"><div style="box-sizing:border-box;">• Three analysts<br style="box-sizing:border-box;"> </div><div style="box-sizing:border-box;">• Starting to hum back then<br style="box-sizing:border-box;"> </div><div style="box-sizing:border-box;">• Engineer left in December - Ben Know - $70,000 -<br style="box-sizing:border-box;"> </div> </div><blockquote style="box-sizing:border-box;margin:0px;border-left-style:solid;padding-left:15px;padding-bottom:1px;padding-top:1px;"><div style="box-sizing:border-box;"><div style="box-sizing:border-box;">• Finishing his undergrad....wanted a long term goal.  ;Wanted to go to sports analytics </div> </div></blockquote><div style="box-sizing:border-box;margin:0px;"><div style="box-sizing:border-box;">• One of the analysts left in december<br style="box-sizing:border-box;"> </div> </div><blockquote style="box-sizing:border-box;margin:0px;border-left-style:solid;padding-left:15px;padding-bottom:1px;padding-top:1px;"><div style="box-sizing:border-box;"><div style="box-sizing:border-box;">• Jason - senior analyst.  ;Had been here two years.  ;Not grasping the tech stack - $100,000 </div> </div></blockquote><div style="box-sizing:border-box;margin:0px;"><div style="box-sizing:border-box;">• DBT - for SQL tools and modeling<br style="box-sizing:border-box;"> </div><div style="box-sizing:border-box;"><br style="box-sizing:border-box;"> </div><div style="box-sizing:border-box;">• They are thinking about moving towards Power BI<br style="box-sizing:border-box;"> </div><div style="box-sizing:border-box;"><span style="box-sizing:border-box;"><br style="box-sizing:border-box;"></span> </div><div style="box-sizing:border-box;"><span style="box-sizing:border-box;">Data sources</span> </div> </div><blockquote style="box-sizing:border-box;margin:0px;border-left-style:solid;padding-left:15px;padding-bottom:1px;padding-top:1px;"><div style="box-sizing:border-box;"><div style="box-sizing:border-box;"><span style="box-sizing:border-box;">• Transactions - E-123 - Enrollment 123 - SAAS product</span> </div> </div></blockquote><blockquote style="box-sizing:border-box;margin:0px;border-left-style:solid;padding-left:15px;padding-bottom:1px;padding-top:1px;"><div style="box-sizing:border-box;"><div style="box-sizing:border-box;">• Pipedrive data for business development </div> </div><div style="box-sizing:border-box;"><br style="box-sizing:border-box;"> </div></blockquote><div style="box-sizing:border-box;margin:0px;"><div style="box-sizing:border-box;">• Opportunity to replace big query to databricks<br style="box-sizing:border-box;"> </div><div style="box-sizing:border-box;"><br style="box-sizing:border-box;"> </div><div style="box-sizing:border-box;">Company profile </div> </div><blockquote style="box-sizing:border-box;margin:0px;border-left-style:solid;padding-left:15px;padding-bottom:1px;padding-top:1px;"><div style="box-sizing:border-box;"><div style="box-sizing:border-box;">• employees - 80 - 90 employees - reps on the phone </div> </div><div style="box-sizing:border-box;">* 15,000 agents use their products to resell to their customers </div><div style="box-sizing:border-box;"><ul style="box-sizing:border-box;padding:0px 0px 0px 32px;margin:4px 0px;"><li style="box-sizing:border-box;">sell dental plans </li> </ul> </div></blockquote><blockquote style="box-sizing:border-box;margin:0px;border-left-style:solid;padding-left:15px;padding-bottom:1px;padding-top:1px;"><div style="box-sizing:border-box;"><div style="box-sizing:border-box;">• near term - not growing to much </div> </div></blockquote><blockquote style="box-sizing:border-box;margin:0px;border-left-style:solid;padding-left:15px;padding-bottom:1px;padding-top:1px;"><div style="box-sizing:border-box;"><div style="box-sizing:border-box;">• no liquidity events </div> </div><div style="box-sizing:border-box;"><span style="box-sizing:border-box;display:inline !important;">• $10,000,000 range in revenue</span><br style="box-sizing:border-box;"> </div></blockquote><div style="box-sizing:border-box;margin:0px;"><br style="box-sizing:border-box;"> </div><div style="box-sizing:border-box;margin:0px;">People </div><blockquote style="box-sizing:border-box;margin:0px;border-left-style:solid;padding-left:15px;padding-bottom:1px;padding-top:1px;"><div style="box-sizing:border-box;"><div style="box-sizing:border-box;">• Sam is very data driven - great- what they want for a leader </div> </div></blockquote><blockquote style="box-sizing:border-box;margin:0px;border-left-style:solid;padding-left:15px;padding-bottom:1px;padding-top:1px;"><div style="box-sizing:border-box;"><span style="box-sizing:border-box;display:inline !important;">• Will is the technology person who leads the engineering team</span> </div><div style="box-sizing:border-box;"><ul style="box-sizing:border-box;padding:0px 0px 0px 32px;margin:4px 0px;"><li style="box-sizing:border-box;"><span style="box-sizing:border-box;display:inline !important;">Future hire: ;<span style="box-sizing:border-box;display:inline !important;">• Emily Andrews - Data Engineer</span></span> </li> </ul><span style="box-sizing:border-box;display:inline !important;"></span> </div></blockquote><div style="box-sizing:border-box;margin:0px;"><div style="box-sizing:border-box;">• A lot of the headaches - pipeline is not easy - very funky - big query<br style="box-sizing:border-box;"> </div><div style="box-sizing:border-box;">• analysts are doing work that they should be - need to be fully focused on this<br style="box-sizing:border-box;"> </div><div style="box-sizing:border-box;">• predictive analytics<br style="box-sizing:border-box;"> </div><div style="box-sizing:border-box;">• each manager needs to be looking<br style="box-sizing:border-box;"> </div><div style="box-sizing:border-box;"><br style="box-sizing:border-box;"> </div><div style="box-sizing:border-box;">•  ;AI initiatives </div><div style="box-sizing:border-box;">• He and his team are focused on AI - Just brought regular things<br style="box-sizing:border-box;"> </div><div style="box-sizing:border-box;">• Making processes more efficient<br style="box-sizing:border-box;"> </div><div style="box-sizing:border-box;">• Created an enrolllment portal </div><div style="box-sizing:border-box;">• contract with independent agents or distributors<br style="box-sizing:border-box;"> </div><div style="box-sizing:border-box;"><br style="box-sizing:border-box;font-size:15px;"> </div> </div>
 </div><div>
 </div><div>
 </div><div> </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/16/2025 5:45:59 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/21/2025 6:04:23 PM'
ON MATCH SET
  c.title= 'NCD Outsourced Data Leader - Jackie Pounds',
  c.description= '<div><img src="https://dev.azure.com/ike0642/bff46545-7652-411a-ad0f-14e800efe36a/_apis/wit/attachments/dbd94026-18f3-425f-8927-8690e8e325b4?fileName=image.png" alt=Image>
 </div><div><div style="box-sizing:border-box;margin:0px;"><span style="box-sizing:border-box;">• Trying to create a data warehouse (google big query, DBT, and Omni BI)</span> </div><div style="box-sizing:border-box;margin:0px;"><div style="box-sizing:border-box;">• Three analysts<br style="box-sizing:border-box;"> </div><div style="box-sizing:border-box;">• Starting to hum back then<br style="box-sizing:border-box;"> </div><div style="box-sizing:border-box;">• Engineer left in December - Ben Know - $70,000 -<br style="box-sizing:border-box;"> </div> </div><blockquote style="box-sizing:border-box;margin:0px;border-left-style:solid;padding-left:15px;padding-bottom:1px;padding-top:1px;"><div style="box-sizing:border-box;"><div style="box-sizing:border-box;">• Finishing his undergrad....wanted a long term goal.  ;Wanted to go to sports analytics </div> </div></blockquote><div style="box-sizing:border-box;margin:0px;"><div style="box-sizing:border-box;">• One of the analysts left in december<br style="box-sizing:border-box;"> </div> </div><blockquote style="box-sizing:border-box;margin:0px;border-left-style:solid;padding-left:15px;padding-bottom:1px;padding-top:1px;"><div style="box-sizing:border-box;"><div style="box-sizing:border-box;">• Jason - senior analyst.  ;Had been here two years.  ;Not grasping the tech stack - $100,000 </div> </div></blockquote><div style="box-sizing:border-box;margin:0px;"><div style="box-sizing:border-box;">• DBT - for SQL tools and modeling<br style="box-sizing:border-box;"> </div><div style="box-sizing:border-box;"><br style="box-sizing:border-box;"> </div><div style="box-sizing:border-box;">• They are thinking about moving towards Power BI<br style="box-sizing:border-box;"> </div><div style="box-sizing:border-box;"><span style="box-sizing:border-box;"><br style="box-sizing:border-box;"></span> </div><div style="box-sizing:border-box;"><span style="box-sizing:border-box;">Data sources</span> </div> </div><blockquote style="box-sizing:border-box;margin:0px;border-left-style:solid;padding-left:15px;padding-bottom:1px;padding-top:1px;"><div style="box-sizing:border-box;"><div style="box-sizing:border-box;"><span style="box-sizing:border-box;">• Transactions - E-123 - Enrollment 123 - SAAS product</span> </div> </div></blockquote><blockquote style="box-sizing:border-box;margin:0px;border-left-style:solid;padding-left:15px;padding-bottom:1px;padding-top:1px;"><div style="box-sizing:border-box;"><div style="box-sizing:border-box;">• Pipedrive data for business development </div> </div><div style="box-sizing:border-box;"><br style="box-sizing:border-box;"> </div></blockquote><div style="box-sizing:border-box;margin:0px;"><div style="box-sizing:border-box;">• Opportunity to replace big query to databricks<br style="box-sizing:border-box;"> </div><div style="box-sizing:border-box;"><br style="box-sizing:border-box;"> </div><div style="box-sizing:border-box;">Company profile </div> </div><blockquote style="box-sizing:border-box;margin:0px;border-left-style:solid;padding-left:15px;padding-bottom:1px;padding-top:1px;"><div style="box-sizing:border-box;"><div style="box-sizing:border-box;">• employees - 80 - 90 employees - reps on the phone </div> </div><div style="box-sizing:border-box;">* 15,000 agents use their products to resell to their customers </div><div style="box-sizing:border-box;"><ul style="box-sizing:border-box;padding:0px 0px 0px 32px;margin:4px 0px;"><li style="box-sizing:border-box;">sell dental plans </li> </ul> </div></blockquote><blockquote style="box-sizing:border-box;margin:0px;border-left-style:solid;padding-left:15px;padding-bottom:1px;padding-top:1px;"><div style="box-sizing:border-box;"><div style="box-sizing:border-box;">• near term - not growing to much </div> </div></blockquote><blockquote style="box-sizing:border-box;margin:0px;border-left-style:solid;padding-left:15px;padding-bottom:1px;padding-top:1px;"><div style="box-sizing:border-box;"><div style="box-sizing:border-box;">• no liquidity events </div> </div><div style="box-sizing:border-box;"><span style="box-sizing:border-box;display:inline !important;">• $10,000,000 range in revenue</span><br style="box-sizing:border-box;"> </div></blockquote><div style="box-sizing:border-box;margin:0px;"><br style="box-sizing:border-box;"> </div><div style="box-sizing:border-box;margin:0px;">People </div><blockquote style="box-sizing:border-box;margin:0px;border-left-style:solid;padding-left:15px;padding-bottom:1px;padding-top:1px;"><div style="box-sizing:border-box;"><div style="box-sizing:border-box;">• Sam is very data driven - great- what they want for a leader </div> </div></blockquote><blockquote style="box-sizing:border-box;margin:0px;border-left-style:solid;padding-left:15px;padding-bottom:1px;padding-top:1px;"><div style="box-sizing:border-box;"><span style="box-sizing:border-box;display:inline !important;">• Will is the technology person who leads the engineering team</span> </div><div style="box-sizing:border-box;"><ul style="box-sizing:border-box;padding:0px 0px 0px 32px;margin:4px 0px;"><li style="box-sizing:border-box;"><span style="box-sizing:border-box;display:inline !important;">Future hire: ;<span style="box-sizing:border-box;display:inline !important;">• Emily Andrews - Data Engineer</span></span> </li> </ul><span style="box-sizing:border-box;display:inline !important;"></span> </div></blockquote><div style="box-sizing:border-box;margin:0px;"><div style="box-sizing:border-box;">• A lot of the headaches - pipeline is not easy - very funky - big query<br style="box-sizing:border-box;"> </div><div style="box-sizing:border-box;">• analysts are doing work that they should be - need to be fully focused on this<br style="box-sizing:border-box;"> </div><div style="box-sizing:border-box;">• predictive analytics<br style="box-sizing:border-box;"> </div><div style="box-sizing:border-box;">• each manager needs to be looking<br style="box-sizing:border-box;"> </div><div style="box-sizing:border-box;"><br style="box-sizing:border-box;"> </div><div style="box-sizing:border-box;">•  ;AI initiatives </div><div style="box-sizing:border-box;">• He and his team are focused on AI - Just brought regular things<br style="box-sizing:border-box;"> </div><div style="box-sizing:border-box;">• Making processes more efficient<br style="box-sizing:border-box;"> </div><div style="box-sizing:border-box;">• Created an enrolllment portal </div><div style="box-sizing:border-box;">• contract with independent agents or distributors<br style="box-sizing:border-box;"> </div><div style="box-sizing:border-box;"><br style="box-sizing:border-box;font-size:15px;"> </div> </div>
 </div><div>
 </div><div>
 </div><div> </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/16/2025 5:45:59 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/21/2025 6:04:23 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'To Do' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '44' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'Kyrus Health - Morgan Beschle',
  c.description= '<div><EMAIL>
 </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '5/1/2025 2:22:36 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/21/2025 6:03:50 PM'
ON MATCH SET
  c.title= 'Kyrus Health - Morgan Beschle',
  c.description= '<div><EMAIL>
 </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '5/1/2025 2:22:36 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/21/2025 6:03:50 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'To Do' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '50' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'Timber - Worqflow - Data Architecture Review and Implementation',
  c.description= '<div>Had an interview with David Wyngaard - d<span style="color:rgb(31, 31, 31);font-family:&quot;Google Sans&quot;, Roboto, Arial, sans-serif;text-align:left;background-color:rgb(240, 244, 249);display:inline !important;"><EMAIL></span> </div><div><span style="color:rgb(31, 31, 31);font-family:&quot;Google Sans&quot;, Roboto, Arial, sans-serif;text-align:left;background-color:rgb(240, 244, 249);display:inline !important;">
</span> </div><div><font color="#1f1f1f" face="Google Sans, Roboto, Arial, sans-serif"><span style="background-color:rgb(240, 244, 249);">He likes me and wants to start a project with me. ; He\'s getting approval from his boss.</span></font> </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '6/24/2025 3:58:22 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/21/2025 6:03:07 PM'
ON MATCH SET
  c.title= 'Timber - Worqflow - Data Architecture Review and Implementation',
  c.description= '<div>Had an interview with David Wyngaard - d<span style="color:rgb(31, 31, 31);font-family:&quot;Google Sans&quot;, Roboto, Arial, sans-serif;text-align:left;background-color:rgb(240, 244, 249);display:inline !important;"><EMAIL></span> </div><div><span style="color:rgb(31, 31, 31);font-family:&quot;Google Sans&quot;, Roboto, Arial, sans-serif;text-align:left;background-color:rgb(240, 244, 249);display:inline !important;">
</span> </div><div><font color="#1f1f1f" face="Google Sans, Roboto, Arial, sans-serif"><span style="background-color:rgb(240, 244, 249);">He likes me and wants to start a project with me. ; He\'s getting approval from his boss.</span></font> </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '6/24/2025 3:58:22 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/21/2025 6:03:07 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'To Do' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '49' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'Delta Dental Northeast - Solliance - Michael',
  c.description= '<div><p dir=ltr style="margin-top:0pt;margin-bottom:0pt;"><span style="font-size:11pt;font-family:\'Google Sans Text\';color:#000000;">Big Oracle bill</span><span style="font-size:11pt;font-family:\'Google Sans Text\';color:#000000;">
</span> </p><p dir=ltr style="margin-top:0pt;margin-bottom:0pt;"><span style="font-size:11pt;font-family:\'Google Sans Text\';color:#000000;">Takes most direction from larger Delta Dental organization</span><span style="font-size:11pt;font-family:\'Google Sans Text\';color:#000000;">
</span><span style="font-size:11pt;font-family:\'Google Sans Text\';color:#000000;">Uses Pentaho for data integration and reporting</span> </p><p dir=ltr style="margin-top:0pt;margin-bottom:0pt;"><span style="font-size:11pt;font-family:\'Google Sans Text\';color:#000000;">Oracle data warehouse</span><span style="color:rgb(0, 0, 0);"> ;- ;</span><span style="font-family:&quot;Google Sans Text&quot;;font-size:11pt;">Old school</span> </p><p dir=ltr style="margin-top:0pt;margin-bottom:0pt;"><span style="font-size:11pt;font-family:\'Google Sans Text\';color:#000000;">Mostly on-premises</span> </p>Snowflake in corp. </div><div>
 </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '6/10/2025 4:43:15 PM',
  c.modifiedBy= 'Scott Reed',
  c.modifiedOn= '7/14/2025 6:58:06 PM'
ON MATCH SET
  c.title= 'Delta Dental Northeast - Solliance - Michael',
  c.description= '<div><p dir=ltr style="margin-top:0pt;margin-bottom:0pt;"><span style="font-size:11pt;font-family:\'Google Sans Text\';color:#000000;">Big Oracle bill</span><span style="font-size:11pt;font-family:\'Google Sans Text\';color:#000000;">
</span> </p><p dir=ltr style="margin-top:0pt;margin-bottom:0pt;"><span style="font-size:11pt;font-family:\'Google Sans Text\';color:#000000;">Takes most direction from larger Delta Dental organization</span><span style="font-size:11pt;font-family:\'Google Sans Text\';color:#000000;">
</span><span style="font-size:11pt;font-family:\'Google Sans Text\';color:#000000;">Uses Pentaho for data integration and reporting</span> </p><p dir=ltr style="margin-top:0pt;margin-bottom:0pt;"><span style="font-size:11pt;font-family:\'Google Sans Text\';color:#000000;">Oracle data warehouse</span><span style="color:rgb(0, 0, 0);"> ;- ;</span><span style="font-family:&quot;Google Sans Text&quot;;font-size:11pt;">Old school</span> </p><p dir=ltr style="margin-top:0pt;margin-bottom:0pt;"><span style="font-size:11pt;font-family:\'Google Sans Text\';color:#000000;">Mostly on-premises</span> </p>Snowflake in corp. </div><div>
 </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '6/10/2025 4:43:15 PM',
  c.modifiedBy= 'Scott Reed',
  c.modifiedOn= '7/14/2025 6:58:06 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'To Do' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '13' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'INDR',
  c.description= '
<ul style="box-sizing:border-box;padding:0px 0px 0px 40px;"><ul> </ul> </ul>

',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/22/2024 6:31:59 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/14/2025 6:23:37 PM'
ON MATCH SET
  c.title= 'INDR',
  c.description= '
<ul style="box-sizing:border-box;padding:0px 0px 0px 40px;"><ul> </ul> </ul>

',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/22/2024 6:31:59 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/14/2025 6:23:37 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'To Do' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '1' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'ASM: Data Architecture',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '1/25/2024 11:01:52 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/14/2025 6:22:13 PM'
ON MATCH SET
  c.title= 'ASM: Data Architecture',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '1/25/2024 11:01:52 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/14/2025 6:22:13 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'Doing' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '42' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'Bankjoy: Sales enablement analytics platform',
  c.description= '<div>Sales enablement is for the salespeople to use </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/21/2025 3:41:31 AM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/14/2025 6:08:51 PM'
ON MATCH SET
  c.title= 'Bankjoy: Sales enablement analytics platform',
  c.description= '<div>Sales enablement is for the salespeople to use </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/21/2025 3:41:31 AM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/14/2025 6:08:51 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'To Do' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '27' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'HEB Groceries: Teradata Conversion to Databricks',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/14/2025 3:50:23 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/14/2025 6:08:00 PM'
ON MATCH SET
  c.title= 'HEB Groceries: Teradata Conversion to Databricks',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/14/2025 3:50:23 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/14/2025 6:08:00 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'To Do' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '33' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'Xantie Partnership - Michael Thurston',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/17/2025 1:02:12 AM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/14/2025 6:07:04 PM'
ON MATCH SET
  c.title= 'Xantie Partnership - Michael Thurston',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/17/2025 1:02:12 AM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/14/2025 6:07:04 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'To Do' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '48' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'Worqflow - Castle Metals',
  c.description= '<div><ul><li>Make metal for aircraft wings and toys and a bunch of other stuff </li><li>Moving Dynamics to 365 </li><li>Want a data platform ready for when they make that migration </li><li>Tracy is setting up the initial meeting </li> </ul> </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '5/21/2025 9:47:47 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/14/2025 6:04:58 PM'
ON MATCH SET
  c.title= 'Worqflow - Castle Metals',
  c.description= '<div><ul><li>Make metal for aircraft wings and toys and a bunch of other stuff </li><li>Moving Dynamics to 365 </li><li>Want a data platform ready for when they make that migration </li><li>Tracy is setting up the initial meeting </li> </ul> </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '5/21/2025 9:47:47 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/14/2025 6:04:58 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'To Do' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '39' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'La Mesa RV - Geoff Pollock',
  c.description= '<div>He wants Scott to review their current software architecture team and make recommendations. </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/17/2025 1:04:49 AM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/14/2025 6:03:56 PM'
ON MATCH SET
  c.title= 'La Mesa RV - Geoff Pollock',
  c.description= '<div>He wants Scott to review their current software architecture team and make recommendations. </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/17/2025 1:04:49 AM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/14/2025 6:03:56 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'To Do' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '46' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'Worqflow - Tracey',
  c.description= '<div>Tracey is friends with Jomboy. </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '5/2/2025 2:15:44 AM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '5/2/2025 2:16:40 AM'
ON MATCH SET
  c.title= 'Worqflow - Tracey',
  c.description= '<div>Tracey is friends with Jomboy. </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '5/2/2025 2:15:44 AM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '5/2/2025 2:16:40 AM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'To Do' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '45' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'Evolve Technologies - Gabby Lauda (from Worqflow)',
  c.description= '<div><EMAIL>
 </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '5/1/2025 3:29:30 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '5/2/2025 2:16:01 AM'
ON MATCH SET
  c.title= 'Evolve Technologies - Gabby Lauda (from Worqflow)',
  c.description= '<div><EMAIL>
 </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '5/1/2025 3:29:30 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '5/2/2025 2:16:01 AM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'To Do' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '43' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'All Covered - Doug Ford',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/23/2025 2:22:59 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '4/23/2025 2:23:00 PM'
ON MATCH SET
  c.title= 'All Covered - Doug Ford',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/23/2025 2:22:59 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '4/23/2025 2:23:00 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'Done' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '3' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'ASM: WebForms to React Conversion.',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '1/25/2024 11:02:20 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '4/22/2025 5:02:31 PM'
ON MATCH SET
  c.title= 'ASM: WebForms to React Conversion.',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '1/25/2024 11:02:20 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '4/22/2025 5:02:31 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'To Do' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '38' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'Bartek Ingredients - Stephen Chambers',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/17/2025 1:03:54 AM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '4/21/2025 3:48:11 AM'
ON MATCH SET
  c.title= 'Bartek Ingredients - Stephen Chambers',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/17/2025 1:03:54 AM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '4/21/2025 3:48:11 AM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'To Do' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '37' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'MBK - David Michaud or Alberto',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/17/2025 1:03:42 AM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '4/21/2025 3:47:22 AM'
ON MATCH SET
  c.title= 'MBK - David Michaud or Alberto',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/17/2025 1:03:42 AM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '4/21/2025 3:47:22 AM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'To Do' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '34' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'Woodside - Aaron & Dan Smith',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/17/2025 1:02:46 AM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '4/21/2025 3:45:29 AM'
ON MATCH SET
  c.title= 'Woodside - Aaron & Dan Smith',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/17/2025 1:02:46 AM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '4/21/2025 3:45:29 AM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'To Do' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '29' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'PNC Bank - Analytics team support - Lu Wang',
  c.description= '<div>An Tran has an analytics team that needs help </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/16/2025 5:40:56 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '4/21/2025 3:45:19 AM'
ON MATCH SET
  c.title= 'PNC Bank - Analytics team support - Lu Wang',
  c.description= '<div>An Tran has an analytics team that needs help </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/16/2025 5:40:56 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '4/21/2025 3:45:19 AM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'To Do' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '26' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'Peak Margins Partnership - Pranav Garg',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/3/2025 5:20:36 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '4/21/2025 3:44:55 AM'
ON MATCH SET
  c.title= 'Peak Margins Partnership - Pranav Garg',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/3/2025 5:20:36 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '4/21/2025 3:44:55 AM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'To Do' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '40' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'Apex Medical - Barry',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/21/2025 12:13:28 AM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '4/21/2025 12:13:29 AM'
ON MATCH SET
  c.title= 'Apex Medical - Barry',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/21/2025 12:13:28 AM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '4/21/2025 12:13:29 AM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'To Do' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '35' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'HealthEquity - Darek Easterly',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/17/2025 1:02:59 AM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '4/17/2025 1:02:59 AM'
ON MATCH SET
  c.title= 'HealthEquity - Darek Easterly',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/17/2025 1:02:59 AM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '4/17/2025 1:02:59 AM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'To Do' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '30' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'Cap Gemini Partnership - Scott Barnes',
  c.description= '<ul><li><div>He\'s senior director of Data &amp; AI at Cap Gemini </div> </li> </ul>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/16/2025 5:41:28 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '4/17/2025 12:55:50 AM'
ON MATCH SET
  c.title= 'Cap Gemini Partnership - Scott Barnes',
  c.description= '<ul><li><div>He\'s senior director of Data &amp; AI at Cap Gemini </div> </li> </ul>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/16/2025 5:41:28 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '4/17/2025 12:55:50 AM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'To Do' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '31' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'CDW Partnership - Rex Washburn',
  c.description= '<div><div style="text-align:left;margin:0in;font-family:Aptos, Aptos_EmbeddedFont, Aptos_MSFontService, Calibri, Helvetica, sans-serif;font-size:10pt;"><b>Rich Borucki</b> </div><div style="text-align:left;margin:0px;font-family:Aptos, Aptos_EmbeddedFont, Aptos_MSFontService, Calibri, Helvetica, sans-serif;font-size:10pt;">Sr. Solutions Architect – Presales | Data &amp; Analytics ;|<span style="color:rgb(204, 0, 0);"> ;CDW Digital Velocity</span> </div><div style="text-align:left;margin:0px;font-family:Aptos, Aptos_EmbeddedFont, Aptos_MSFontService, Calibri, Helvetica, sans-serif;font-size:10pt;">Mobile: ************ </div><div style="text-align:left;margin:0px;font-family:Aptos, Aptos_EmbeddedFont, Aptos_MSFontService, Calibri, Helvetica, sans-serif;font-size:10pt;"><span style="color:rgb(5, 99, 193);"><u><a href="http://www.cdw.com/" title="http://www.cdw.com/" target=_blank style="color:rgb(17, 85, 204);">www.cdw.com</a></u></span> ;|<span> ;</span><span style="color:rgb(5, 99, 193);"><u><a href="mailto:<EMAIL>" title="mailto:<EMAIL>" target=_blank style="color:rgb(17, 85, 204);"><EMAIL></a></u></span> </div><div style="text-align:left;margin:0px;font-family:Aptos, Aptos_EmbeddedFont, Aptos_MSFontService, Calibri, Helvetica, sans-serif;font-size:10pt;">LinkedIn:<span> ;</span><a href="http://www.linkedin.com/in/richborucki" title="www.linkedin.com/in/richborucki" target=_blank style="color:rgb(17, 85, 204);">/richborucki</a> </div>
 </div><div>
 </div><div><span style="color:rgb(119, 119, 119);font-family:&quot;Google Sans&quot;, Roboto, RobotoDraft, Helvetica, Arial, sans-serif;display:inline !important;">Rex Washburn - Senior Director of Data &amp; Analytics</span>
 </div><div><span style="color:rgb(119, 119, 119);font-family:&quot;Google Sans&quot;, Roboto, RobotoDraft, Helvetica, Arial, sans-serif;display:inline !important;">
</span> </div><div><span style="font-family:&quot;Google Sans&quot;, Roboto, RobotoDraft, Helvetica, Arial, sans-serif;display:inline !important;"><span style="font-family:&quot;Google Sans&quot;, Roboto, RobotoDraft, Helvetica, Arial, sans-serif;"><font color="#777777">Paul Zajdel</font><font color="#222222"> ;- Senior BD ;</font></span>
</span> </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/16/2025 5:43:25 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '4/17/2025 12:55:20 AM'
ON MATCH SET
  c.title= 'CDW Partnership - Rex Washburn',
  c.description= '<div><div style="text-align:left;margin:0in;font-family:Aptos, Aptos_EmbeddedFont, Aptos_MSFontService, Calibri, Helvetica, sans-serif;font-size:10pt;"><b>Rich Borucki</b> </div><div style="text-align:left;margin:0px;font-family:Aptos, Aptos_EmbeddedFont, Aptos_MSFontService, Calibri, Helvetica, sans-serif;font-size:10pt;">Sr. Solutions Architect – Presales | Data &amp; Analytics ;|<span style="color:rgb(204, 0, 0);"> ;CDW Digital Velocity</span> </div><div style="text-align:left;margin:0px;font-family:Aptos, Aptos_EmbeddedFont, Aptos_MSFontService, Calibri, Helvetica, sans-serif;font-size:10pt;">Mobile: ************ </div><div style="text-align:left;margin:0px;font-family:Aptos, Aptos_EmbeddedFont, Aptos_MSFontService, Calibri, Helvetica, sans-serif;font-size:10pt;"><span style="color:rgb(5, 99, 193);"><u><a href="http://www.cdw.com/" title="http://www.cdw.com/" target=_blank style="color:rgb(17, 85, 204);">www.cdw.com</a></u></span> ;|<span> ;</span><span style="color:rgb(5, 99, 193);"><u><a href="mailto:<EMAIL>" title="mailto:<EMAIL>" target=_blank style="color:rgb(17, 85, 204);"><EMAIL></a></u></span> </div><div style="text-align:left;margin:0px;font-family:Aptos, Aptos_EmbeddedFont, Aptos_MSFontService, Calibri, Helvetica, sans-serif;font-size:10pt;">LinkedIn:<span> ;</span><a href="http://www.linkedin.com/in/richborucki" title="www.linkedin.com/in/richborucki" target=_blank style="color:rgb(17, 85, 204);">/richborucki</a> </div>
 </div><div>
 </div><div><span style="color:rgb(119, 119, 119);font-family:&quot;Google Sans&quot;, Roboto, RobotoDraft, Helvetica, Arial, sans-serif;display:inline !important;">Rex Washburn - Senior Director of Data &amp; Analytics</span>
 </div><div><span style="color:rgb(119, 119, 119);font-family:&quot;Google Sans&quot;, Roboto, RobotoDraft, Helvetica, Arial, sans-serif;display:inline !important;">
</span> </div><div><span style="font-family:&quot;Google Sans&quot;, Roboto, RobotoDraft, Helvetica, Arial, sans-serif;display:inline !important;"><span style="font-family:&quot;Google Sans&quot;, Roboto, RobotoDraft, Helvetica, Arial, sans-serif;"><font color="#777777">Paul Zajdel</font><font color="#222222"> ;- Senior BD ;</font></span>
</span> </div>',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/16/2025 5:43:25 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '4/17/2025 12:55:20 AM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'Done' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '2' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'ASM: Shashank .NET Core Conversion',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '1/25/2024 11:02:07 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '4/14/2025 6:07:01 PM'
ON MATCH SET
  c.title= 'ASM: Shashank .NET Core Conversion',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '1/25/2024 11:02:07 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '4/14/2025 6:07:01 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'To Do' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '23' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'Export AzureDevOps board data',
  c.description= '',
  c.createdBy= 'Shashank Shekhar',
  c.createdOn= '1/29/2025 7:51:20 PM',
  c.modifiedBy= 'Shashank Shekhar',
  c.modifiedOn= '4/3/2025 1:27:38 PM'
ON MATCH SET
  c.title= 'Export AzureDevOps board data',
  c.description= '',
  c.createdBy= 'Shashank Shekhar',
  c.createdOn= '1/29/2025 7:51:20 PM',
  c.modifiedBy= 'Shashank Shekhar',
  c.modifiedOn= '4/3/2025 1:27:38 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'Done' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '17' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'Engle Martin - Migrate ADF to Databricks',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '6/24/2024 6:39:55 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '3/31/2025 6:08:28 PM'
ON MATCH SET
  c.title= 'Engle Martin - Migrate ADF to Databricks',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '6/24/2024 6:39:55 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '3/31/2025 6:08:28 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'Done' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '12' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'KB Homes:  Machine Learning and Data Platform',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/8/2024 6:01:25 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '3/31/2025 6:08:11 PM'
ON MATCH SET
  c.title= 'KB Homes:  Machine Learning and Data Platform',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '4/8/2024 6:01:25 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '3/31/2025 6:08:11 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'Done' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '11' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'Chrome Hearts: Data Platform Creation',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '3/4/2024 8:00:43 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '3/31/2025 6:08:08 PM'
ON MATCH SET
  c.title= 'Chrome Hearts: Data Platform Creation',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '3/4/2024 8:00:43 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '3/31/2025 6:08:08 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'Done' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '21' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'TM: SRE for Mountain View',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '8/26/2024 6:46:06 PM',
  c.modifiedBy= 'Scott Reed',
  c.modifiedOn= '2/3/2025 7:35:53 PM'
ON MATCH SET
  c.title= 'TM: SRE for Mountain View',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '8/26/2024 6:46:06 PM',
  c.modifiedBy= 'Scott Reed',
  c.modifiedOn= '2/3/2025 7:35:53 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'Done' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '14' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'Verify: Executive Data Consulting',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '5/20/2024 6:25:45 PM',
  c.modifiedBy= 'Scott Reed',
  c.modifiedOn= '1/7/2025 7:33:38 PM'
ON MATCH SET
  c.title= 'Verify: Executive Data Consulting',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '5/20/2024 6:25:45 PM',
  c.modifiedBy= 'Scott Reed',
  c.modifiedOn= '1/7/2025 7:33:38 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'Done' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '15' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'TM: XBRL and Edgar document ingestion and RAG platform implementation',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '6/3/2024 6:42:14 PM',
  c.modifiedBy= 'Scott Reed',
  c.modifiedOn= '1/7/2025 7:32:18 PM'
ON MATCH SET
  c.title= 'TM: XBRL and Edgar document ingestion and RAG platform implementation',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '6/3/2024 6:42:14 PM',
  c.modifiedBy= 'Scott Reed',
  c.modifiedOn= '1/7/2025 7:32:18 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'Done' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '16' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'BankJoy - Build Data Platform Architecture',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '6/24/2024 6:36:23 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '10/21/2024 6:33:27 PM'
ON MATCH SET
  c.title= 'BankJoy - Build Data Platform Architecture',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '6/24/2024 6:36:23 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '10/21/2024 6:33:27 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'Done' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '6' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'TM: DPS Audit and Review',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '1/25/2024 11:06:48 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '10/21/2024 6:31:36 PM'
ON MATCH SET
  c.title= 'TM: DPS Audit and Review',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '1/25/2024 11:06:48 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '10/21/2024 6:31:36 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'Done' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '9' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'Construction: Initial Design',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '1/25/2024 11:07:42 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '10/14/2024 6:22:59 PM'
ON MATCH SET
  c.title= 'Construction: Initial Design',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '1/25/2024 11:07:42 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '10/14/2024 6:22:59 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'Done' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '22' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'Shashank H1B app',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '8/26/2024 6:48:28 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '10/14/2024 6:22:48 PM'
ON MATCH SET
  c.title= 'Shashank H1B app',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '8/26/2024 6:48:28 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '10/14/2024 6:22:48 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'Done' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '19' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'TM: Liferay Code Review',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '7/22/2024 6:39:31 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '8/12/2024 6:33:40 PM'
ON MATCH SET
  c.title= 'TM: Liferay Code Review',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '7/22/2024 6:39:31 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '8/12/2024 6:33:40 PM'
;
MERGE (i:Import { url: 'https://dev.azure.com/ike0642/craftingbytes' })
ON CREATE SET i.externalId = randomUUID()
MERGE (i)<-[:IMPORTED_FROM]-(b:Board { title: 'craftingbytes' })
ON CREATE SET b.externalId = randomUUID()
MERGE (b)<-[:IS_STATE_IN]-(col:Column { name: 'Done' })
ON CREATE SET col.externalId = randomUUID()
MERGE (col)<-[:HAS_STATE]-(c:Card { importedId: '7' } )
ON CREATE SET
  c.externalId = randomUUID(),
  c.title= 'TM: Clic Audit and Review',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '1/25/2024 11:07:12 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/22/2024 6:32:23 PM'
ON MATCH SET
  c.title= 'TM: Clic Audit and Review',
  c.description= '',
  c.createdBy= 'Ike Ellis',
  c.createdOn= '1/25/2024 11:07:12 PM',
  c.modifiedBy= 'Ike Ellis',
  c.modifiedOn= '7/22/2024 6:32:23 PM'
;
