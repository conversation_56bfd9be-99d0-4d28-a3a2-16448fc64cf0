# Dockerfile for Neo4j

FROM neo4j:latest

ARG NEO4J_USER
ARG NEO4J_PASSWORD
ENV NEO4J_AUTH="neo4j/$NEO4J_PASSWORD"

# Download the APOC jar file from the official GitHub release.
# (Be sure to adjust the version URL as needed based on the APOC release you want.)
#https://github.com/neo4j-contrib/neo4j-apoc-procedures/releases/download/4.4.0.35/apoc-4.4.0.35-all.jar
RUN wget -O /var/lib/neo4j/plugins/apoc.jar \
    https://github.com/neo4j-contrib/neo4j-apoc-procedures/releases/download/4.4.0.35/apoc-4.4.0.35-all.jar

ENV NEO4JLABS_PLUGINS='["apoc"]'
ENV NEO4J_dbms_security_procedures_unrestricted=apoc.*
ENV NEO4J_apoc_export_file_enabled=true
ENV NEO4J_apoc_import_file_enabled=true

#VOLUME ../mount
VOLUME /Users/<USER>/Documents/CraftingBytes/kanban/mount
