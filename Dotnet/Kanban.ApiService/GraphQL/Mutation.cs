using Kanban.ApiService.Models;

namespace Kanban.ApiService.GraphQL;

using Neo4j.Driver;
using HotChocolate;
using HotChocolate.Types;

public class CardInput
{
    public string Title { get; set; }
    public string Description { get; set; }
    public bool IsArchived { get; set; }
    public bool IsCompleted { get; set; }
    public int? ParentId { get; set; } // If it's a child card, we accept the ParentId
}

public class Mutation
{
    private readonly IDriver _neo4jDriver;

    public Mutation(IDriver neo4jDriver)
    {
        _neo4jDriver = neo4jDriver;
    }

    public async Task<Card> AddCardAsync(CardInput input)
    {
        var session = _neo4jDriver.AsyncSession();
        var newCard = new Card
        {
            Title = input.Title,
            Description = input.Description,
            IsArchived = input.IsArchived,
            IsCompleted = input.IsCompleted,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            Parent = null, // This will be set later if ParentId is provided
            Children = new List<Card>()
        };

        try
        {
            var query = @"
                CREATE (c:Card { Title: $title, Description: $description, CreatedAt: $createdAt, UpdatedAt: $updatedAt, IsArchived: $isArchived, IsCompleted: $isCompleted })
                RETURN c";
            
            var parameters = new Dictionary<string, object>
            {
                { "title", newCard.Title },
                { "description", newCard.Description },
                { "createdAt", newCard.CreatedAt },
                { "updatedAt", newCard.UpdatedAt },
                { "isArchived", newCard.IsArchived },
                { "isCompleted", newCard.IsCompleted }
            };

            var result = await session.RunAsync(query, parameters);
            var cardRecord = await result.SingleAsync();
            var cardId = cardRecord["c"].As<INode>().Properties["Id"].As<int>();

            newCard.Id = cardId;

            // If the card has a parent, create the relationship
            if (input.ParentId.HasValue)
            {
                var parentQuery = @"
                    MATCH (c:Card { Id: $cardId })
                    MATCH (p:Card { Id: $parentId })
                    MERGE (p)-[:PARENT_OF]->(c)";
                
                await session.RunAsync(parentQuery, new Dictionary<string, object>
                {
                    { "cardId", newCard.Id },
                    { "parentId", input.ParentId.Value }
                });
            }
        }
        finally
        {
            await session.CloseAsync();
        }

        return newCard;
    }
}
