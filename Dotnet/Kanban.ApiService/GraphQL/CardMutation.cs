using HotChocolate;
using HotChocolate.Data;
using Kanban.ApiService.Models;
using Neo4j.Driver;
using INode = HotChocolate.Types.Relay.INode;

namespace Kanban.ApiService.GraphQL
{
    [ExtendObjectType(Name = "Mutation")]
    public class CardMutation
    {
        [GraphQLName("createCard")]
        public async Task<Card> CreateCardAsync(
            [Service(ServiceKind.Pooled)] IAsyncSession session,
            string title,
            string description)
        {
            var card = new Card
            {
                Title = title,
                Description = description,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                IsDeleted = false,
                IsArchived = false,
                IsCompleted = false
            };

            var createCardQuery = @"
                CREATE (c:Card { Id: $id, Title: $title, Description: $description, 
                                 CreatedAt: $createdAt, UpdatedAt: $updatedAt, 
                                 IsDeleted: $isDeleted, IsArchived: $isArchived, 
                                 IsCompleted: $isCompleted })
                RETURN c";

            var parameters = new Dictionary<string, object>
            {
                { "id", card.Id },
                { "title", card.Title },
                { "description", card.Description },
                { "createdAt", card.CreatedAt },
                { "updatedAt", card.UpdatedAt },
                { "isDeleted", card.IsDeleted },
                { "isArchived", card.IsArchived },
                { "isCompleted", card.IsCompleted }
            };

            var result = await session.RunAsync(createCardQuery, parameters);
            var record = await result.SingleAsync();
            var node = record["c"].As<INode>();

            // card.Id = node.  node.Properties["Id"].As<int>();

            return card;
        }
    }
}