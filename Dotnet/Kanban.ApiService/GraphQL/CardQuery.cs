using HotChocolate.Data;
using HotChocolate.Data.Neo4J;
using HotChocolate.Data.Neo4J.Execution;
using Kanban.ApiService.Models;
using Neo4j.Driver;

namespace Kanban.ApiService.GraphQL
{
	[ExtendObjectType(Name = "Query")]
	public class CardQuery
	{
		[GraphQLName("cards")]
		[UseNeo4JDatabase(databaseName: "neo4j")]
		[UseProjection]
		[UseFiltering]
		[UseSorting]
		public Neo4JExecutable<Card> GetCards(
			[Service(ServiceKind.Resolver)] IAsyncSession session) =>
			new (session);
	}
	
}
