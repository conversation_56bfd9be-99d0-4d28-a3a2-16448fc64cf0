using Kanban.ApiService.Models;
using Neo4j.Driver;
using Newtonsoft.Json;

namespace Kanban.ApiService;

    public class Neo4JSeederService : IDisposable
    {
        private readonly IDriver _driver;

        public Neo4JSeederService(IDriver driver)
        {
            _driver = driver;
        }

        // Method to seed data
        public async Task SeedDatabaseFromJsonAsync(string jsonFilePath)
        {
            var jsonData = await File.ReadAllTextAsync(jsonFilePath);
            var cards = JsonConvert.DeserializeObject<List<Card>>(jsonData);
            
            if (cards == null)
            {
                throw new Exception("Unable to parse JSON data");
            }

            var session = _driver.AsyncSession();
            try
            {
                foreach (var card in cards)
                {
                    // Insert each card node
                    string createCardQuery = @"
                        MERGE (c:Card { Id: $id })
                        SET c.Title = $title, c.Description = $description, 
                            c.CreatedAt = $createdAt, c.UpdatedAt = $updatedAt, 
                            c.IsDeleted = $isDeleted, c.IsArchived = $isArchived, 
                            c.IsCompleted = $isCompleted";
                    
                    var parameters = new Dictionary<string, object>
                    {
                        { "id", card.Id },
                        { "title", card.Title },
                        { "description", card.Description },
                        { "createdAt", card.CreatedAt },
                        { "updatedAt", card.UpdatedAt },
                        { "isDeleted", card.IsDeleted },
                        { "isArchived", card.IsArchived },
                        { "isCompleted", card.IsCompleted }
                    };
                    
                    await session.RunAsync(createCardQuery, parameters);

                    // If card has a parent, create the CARD_IN relationship
                    if (card.Parent != null)
                    {
                        string createParentRelationshipQuery = @"
                            MATCH (c:Card { Id: $cardId })
                            MATCH (p:Card { Id: $parentId })
                            MERGE (c)-[:CARD_IN]->(p)";
                        
                        var parentRelationshipParams = new Dictionary<string, object>
                        {
                            { "cardId", card.Id },
                            { "parentId", card.Parent.Id }
                        };

                        await session.RunAsync(createParentRelationshipQuery, parentRelationshipParams);
                    }

                    // If card has children, create the PARENT_OF relationships
                    if (card.Children != null && card.Children.Count > 0)
                    {
                        foreach (var child in card.Children)
                        {
                            string createChildRelationshipQuery = @"
                                MATCH (c:Card { Id: $cardId })
                                MATCH (child:Card { Id: $childId })
                                MERGE (c)-[:PARENT_OF]->(child)";
                            
                            var childRelationshipParams = new Dictionary<string, object>
                            {
                                { "cardId", card.Id },
                                { "childId", child.Id }
                            };

                            await session.RunAsync(createChildRelationshipQuery, childRelationshipParams);
                        }
                    }
                }
            }
            finally
            {
                await session.CloseAsync();
            }
        }

        public void Dispose()
        {
            _driver?.Dispose();
        }
    }
