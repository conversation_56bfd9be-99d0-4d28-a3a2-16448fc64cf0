using HotChocolate.Data.Neo4J;

namespace Kanban.ApiService.Models
{
	public class Card
	{
		public int Id { get; set; }
		public string Title { get; set; }
		public string Description { get; set; }
		public DateTime CreatedAt { get; set; }
		public DateTime UpdatedAt { get; set; }
		public DateTime? DeletedAt { get; set; }
		public bool IsDeleted { get; set; }
		public bool IsArchived { get; set; }
		public bool IsCompleted { get; set; }
		[Neo4JRelationship(Relationships.CARD_IN)]
		public  Card Parent { get; set; }
		[Neo4JRelationship(Relationships.PARENT_OF)]
		public List<Card> Children { get; set; }
	}
}
