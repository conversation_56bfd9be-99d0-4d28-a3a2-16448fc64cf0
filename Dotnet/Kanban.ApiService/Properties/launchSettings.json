{"$schema": "https://json.schemastore.org/launchsettings.json", "profiles": {"http": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "launchUrl": "graphql", "applicationUrl": "http://localhost:5304", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "https": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "launchUrl": "graphql", "applicationUrl": "https://localhost:7344;http://localhost:5304", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}