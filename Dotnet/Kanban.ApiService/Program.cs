using Kanban.ApiService;
using Kanban.ApiService.GraphQL;
using Neo4j.Driver;

var builder = WebApplication.CreateBuilder(args);

// Add service defaults & Aspire components.
builder.AddServiceDefaults();

// Add services to the container.
builder.Services.AddProblemDetails();

// Connect to the database.
IDriver driver = GraphDatabase.Driver(
	"bolt://localhost:7687", 
	AuthTokens.Basic("neo4j", "testpassword"));
builder.Services.AddSingleton(driver);

// Register IAsyncSession
builder.Services.AddScoped<IAsyncSession>(provider =>
	provider.GetRequiredService<IDriver>().AsyncSession());

// Add GraphQL services.
builder.Services
	.AddGraphQLServer()
	.AddQueryType(q => q.Name("Query"))
	.AddType<CardQuery>()
	.AddMaxExecutionDepthRule(5)
	.AddFiltering()
	.AddSorting()
	.AddProjections()
	.AddMutationType<Mutation>();

// Configure OpenTelemetry
builder.Services.ConfigureInstrumentation();

var app = builder.Build();

// Configure the HTTP request pipeline.
app.UseExceptionHandler();

app.MapGet("/seed", async () =>
{
	await new Neo4JSeederService(driver).SeedDatabaseFromJsonAsync("seed.json");
	return "Seed completed";
});
app.MapDefaultEndpoints();
app.MapGraphQL();
app.Run();

record WeatherForecast(DateOnly Date, int TemperatureC, string? Summary)
{
    public int TemperatureF => 32 + (int)(TemperatureC / 0.5556);
}
