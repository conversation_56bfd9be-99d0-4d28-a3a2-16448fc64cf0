using System.Diagnostics;
using System.Diagnostics.Metrics;

namespace Kanban.ApiService;

public static  class Instrumentation
{
    public static IServiceCollection ConfigureInstrumentation(this IServiceCollection service)
    {
        // Custom metrics for the application
        var greeterMeter = new Meter("OTel.Example", "1.0.0");
        var countGreetings = greeterMeter.CreateCounter<int>("greetings.count", description: "Counts the number of greetings");

// Custom ActivitySource for the application
        var greeterActivitySource = new ActivitySource("OTel.Example");
        return service;
    }
}