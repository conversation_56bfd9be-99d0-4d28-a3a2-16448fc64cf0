using Microsoft.AspNetCore.Mvc;
using Neo4j.Driver;

namespace Kanban.ApiService;

public class SeedController : Controller
{
    private readonly IDriver _driver = GraphDatabase.Driver(
        "bolt://localhost:7687", 
        AuthTokens.Basic("neo4j", "testpassword"));
    // GET
    public async Task<IActionResult> Index()
    {
        await new Neo4JSeederService(_driver).SeedDatabaseFromJsonAsync("seed.json");

        return Ok("Seed completed");
    }
}