Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.11.35312.102
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Kanban.AppHost", "Kanban.AppHost\Kanban.AppHost.csproj", "{28E21104-7309-4594-8B04-2E3542A7D640}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Kanban.ServiceDefaults", "Kanban.ServiceDefaults\Kanban.ServiceDefaults.csproj", "{3E4CB55B-3EFC-4780-93F2-88F91ADC1AB2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Kanban.ApiService", "Kanban.ApiService\Kanban.ApiService.csproj", "{225F438C-6F8A-489A-9F2B-4F0D893319A7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Kanban.Tests", "Kanban.Tests\Kanban.Tests.csproj", "{1805219D-8203-4F41-A50D-C85400D5F6BE}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "containers", "containers", "{89CF48EC-0524-46F9-A982-25262870DD19}"
	ProjectSection(SolutionItems) = preProject
		containers\Dockerfile = containers\Dockerfile
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Kanban.Integrations", "Kanban.Integrations\Kanban.Integrations.csproj", "{72732E9E-02B2-49AC-B373-0299DD29AB4F}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{28E21104-7309-4594-8B04-2E3542A7D640}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{28E21104-7309-4594-8B04-2E3542A7D640}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{28E21104-7309-4594-8B04-2E3542A7D640}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{28E21104-7309-4594-8B04-2E3542A7D640}.Release|Any CPU.Build.0 = Release|Any CPU
		{3E4CB55B-3EFC-4780-93F2-88F91ADC1AB2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3E4CB55B-3EFC-4780-93F2-88F91ADC1AB2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3E4CB55B-3EFC-4780-93F2-88F91ADC1AB2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3E4CB55B-3EFC-4780-93F2-88F91ADC1AB2}.Release|Any CPU.Build.0 = Release|Any CPU
		{225F438C-6F8A-489A-9F2B-4F0D893319A7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{225F438C-6F8A-489A-9F2B-4F0D893319A7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{225F438C-6F8A-489A-9F2B-4F0D893319A7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{225F438C-6F8A-489A-9F2B-4F0D893319A7}.Release|Any CPU.Build.0 = Release|Any CPU
		{1805219D-8203-4F41-A50D-C85400D5F6BE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1805219D-8203-4F41-A50D-C85400D5F6BE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1805219D-8203-4F41-A50D-C85400D5F6BE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1805219D-8203-4F41-A50D-C85400D5F6BE}.Release|Any CPU.Build.0 = Release|Any CPU
		{72732E9E-02B2-49AC-B373-0299DD29AB4F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{72732E9E-02B2-49AC-B373-0299DD29AB4F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{72732E9E-02B2-49AC-B373-0299DD29AB4F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{72732E9E-02B2-49AC-B373-0299DD29AB4F}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {1B160252-F0C5-4DFB-9408-B34DB093A513}
	EndGlobalSection
EndGlobal
