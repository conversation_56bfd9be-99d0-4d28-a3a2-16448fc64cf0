using Kanban.AppHost;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;

var builder = DistributedApplication.CreateBuilder(args);

var cache = builder.AddRedis("cache");

var neo4JHttp = builder.Configuration.GetValue<int>("neo4JHttp");
var neo4JBolt = builder.Configuration.GetValue<int>("neo4JBolt");

var neo4jUser = builder.Configuration.GetValue<string>("neo4jUser");
var neo4JPassword = builder.Configuration.GetValue<string>("neo4jPassword");
var mountPath = builder.Configuration.GetValue<string>("mountPath");

var neo4jContainer = builder
  .AddDockerfile("neo4j", "../containers")
  .WithBindMount("VolumeMount.AppHost-neo4j-data", "/var/opt/neo4j")
  .WithBindMount(mountPath, "/var/lib/neo4j/import/")
  .WithBuildArg("NEO4J_USER", neo4jUser)
  .WithBuildArg("NEO4J_PASSWORD", neo4JPassword)
  .WithHttpEndpoint(neo4JHttp, 7474)
  .WithEndpoint(neo4JBolt, 7687)
  .WithLifetime(ContainerLifetime.Persistent);

var apiService = builder
    .AddProject<Projects.Kanban_ApiService>("apiservice");

var svelte = builder
    .AddBunApp("svelte", "../apps/webs", "dev")
    .WithReference(apiService)
    .WithReference(cache)
    // .WithHttpEndpoint(env: "PORT")
    .WithHttpEndpoint(3000, 3300)
    .WithExternalHttpEndpoints()
    .PublishAsDockerFile();

var launchProfile = builder.Configuration["DOTNET_LAUNCH_PROFILE"] ??
                    builder.Configuration
                        ["AppHost:DefaultLaunchProfileName"]; // work around https://github.com/dotnet/aspire/issues/5093
if (builder.Environment.IsDevelopment() && launchProfile == "https")
{
    // Disable TLS certificate validation in development, see https://github.com/dotnet/aspire/issues/3324 for more details.
    svelte.WithEnvironment("NODE_TLS_REJECT_UNAUTHORIZED", "0");
}

builder.Build().Run();
